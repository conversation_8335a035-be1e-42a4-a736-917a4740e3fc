import { createDoc, readDoc, updateDoc<PERSON>ields, deleteDocByPath, readCollection } from "./firestoreService";

export interface LearningPlan {
  id: string;
  title: string;
  description: string;
  topic: string;
  certificateId?: string;
  framework?: string;
  intensity: "detailed" | "general" | "simple";
  focusAreas: string[];
  locale: string;
  userId: string;
  
  // AI-generated content
  objectives: string[];
  estimatedDuration: string;
  difficulty: "Beginner" | "Intermediate" | "Advanced";
  prerequisites: string[];
  
  // Learning tools configuration
  tools: {
    flashcards: boolean;
    trueOrFalse: boolean;
    twoFactsOneLie: boolean;
    knowledgeGraphs: boolean;
    practicalScenarios: boolean;
    matchConcepts: boolean;
    conceptsGuidedLearning: boolean;
  };
  
  // Progress tracking
  progress: {
    currentStep: number;
    totalSteps: number;
    completedTools: string[];
    timeSpent: number; // in minutes
    lastAccessedAt?: Date;
  };
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  status: "draft" | "active" | "completed" | "archived";
}

export interface LearningPlanInput {
  title: string;
  description: string;
  topic: string;
  certificateId?: string;
  framework?: string;
  intensity: "detailed" | "general" | "simple";
  focusAreas: string[];
  locale: string;
  userId: string;
}

export interface LearningToolSession {
  id: string;
  planId: string;
  toolType: string;
  userId: string;
  content: any; // Tool-specific content
  progress: {
    completed: boolean;
    score?: number;
    timeSpent: number;
    attempts: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Creates a new learning plan
 */
export async function createLearningPlan(input: LearningPlanInput): Promise<string> {
  try {
    const planId = `plan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const path = `plans/${planId}`;

    const planData = {
      id: planId,
      ...input,
      objectives: [],
      estimatedDuration: "",
      difficulty: "Beginner",
      prerequisites: [],
      tools: {
        flashcards: true,
        trueOrFalse: true,
        twoFactsOneLie: true,
        knowledgeGraphs: true,
        practicalScenarios: true,
        matchConcepts: true,
        conceptsGuidedLearning: true,
      },
      progress: {
        currentStep: 0,
        totalSteps: 7, // Number of available tools
        completedTools: [],
        timeSpent: 0,
      },
      status: "draft",
    };

    await createDoc(path, planData);
    return planId;
  } catch (error) {
    console.error("Error in createLearningPlan:", error);
    throw error;
  }
}

/**
 * Gets a learning plan by ID
 */
export async function getLearningPlan(planId: string): Promise<LearningPlan | null> {
  const path = `plans/${planId}`;
  return await readDoc<LearningPlan>(path);
}

/**
 * Updates a learning plan
 */
export async function updateLearningPlan(
  planId: string,
  updates: Partial<LearningPlan>
): Promise<void> {
  const path = `plans/${planId}`;
  await updateDocFields(path, updates);
}

/**
 * Gets all learning plans for a user
 */
export async function getUserLearningPlans(userId: string): Promise<LearningPlan[]> {
  const allPlans = await readCollection<LearningPlan>('plans');
  return allPlans.filter(plan => plan.userId === userId);
}

/**
 * Deletes a learning plan
 */
export async function deleteLearningPlan(planId: string): Promise<void> {
  const path = `plans/${planId}`;
  await deleteDocByPath(path);
}

/**
 * Updates plan progress
 */
export async function updatePlanProgress(
  planId: string,
  progress: Partial<LearningPlan['progress']>
): Promise<void> {
  const path = `plans/${planId}`;
  await updateDocFields(path, {
    progress: {
      ...progress,
      lastAccessedAt: new Date(),
    },
  });
}

/**
 * Creates a learning tool session
 */
export async function createToolSession(
  planId: string,
  toolType: string,
  userId: string,
  content: any
): Promise<string> {
  const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const path = `plans/${planId}/sessions/${sessionId}`;
  
  const sessionData: LearningToolSession = {
    id: sessionId,
    planId,
    toolType,
    userId,
    content,
    progress: {
      completed: false,
      timeSpent: 0,
      attempts: 0,
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  await createDoc(path, sessionData);
  return sessionId;
}

/**
 * Gets tool sessions for a plan
 */
export async function getPlanToolSessions(planId: string): Promise<LearningToolSession[]> {
  const path = `plans/${planId}/sessions`;
  return await readCollection<LearningToolSession>(path);
}

/**
 * Updates a tool session
 */
export async function updateToolSession(
  planId: string,
  sessionId: string,
  updates: Partial<LearningToolSession>
): Promise<void> {
  const path = `plans/${planId}/sessions/${sessionId}`;
  await updateDocFields(path, {
    ...updates,
    updatedAt: new Date(),
  });
}
