"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/Card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { useUser } from "@/hooks/useUser";
import { useLocale } from "next-intl";
import { 
  Brain, 
  Target, 
  Clock, 
  BookOpen,
  Zap,
  TrendingUp,
  Award,
  Play,
  Pause,
  RotateCcw,
  ChevronRight,
  ChevronLeft,
  Settings,
  Star,
  CheckCircle,
  Lightbulb,
  Users,
  Globe,
  Layers
} from "lucide-react";
import { toast } from "sonner";
import { type LearningPlan } from "@/Services/planService";

// Import learning tool components
import Flashcards from "@/components/ui/learning-tools/Flashcards";
import TrueOrFalse from "@/components/ui/learning-tools/TrueOrFalse";
import TwoFactsOne<PERSON>ie from "@/components/ui/learning-tools/TwoFactsOneLie";
import KnowledgeGraphs from "@/components/ui/learning-tools/KnowledgeGraphs";
import PracticalScenarioGuidance from "@/components/ui/learning-tools/PracticalScenarioGuidance";
import MatchConcepts from "@/components/ui/learning-tools/MatchConcepts";
import ConceptsGuidedLearning from "@/components/ui/learning-tools/ConceptsGuidedLearning";

interface LearningLessonProps {
  plan: LearningPlan;
  onPlanUpdate?: (updates: Partial<LearningPlan>) => void;
  onComplete?: () => void;
}

interface ToolSession {
  id: string;
  toolType: string;
  content: any;
  progress: {
    completed: boolean;
    score?: number;
    timeSpent: number;
    attempts: number;
  };
}

const TOOL_CONFIGS = {
  flashcards: {
    name: "Flashcards",
    description: "Master concepts through active recall",
    icon: BookOpen,
    color: "emerald",
    estimatedTime: "10-15 min"
  },
  trueOrFalse: {
    name: "True or False",
    description: "Test your knowledge with precision",
    icon: Target,
    color: "bright-green",
    estimatedTime: "8-12 min"
  },
  twoFactsOneLie: {
    name: "Two Facts, One Lie",
    description: "Sharpen your critical thinking",
    icon: Brain,
    color: "lime-green",
    estimatedTime: "12-18 min"
  },
  knowledgeGraphs: {
    name: "Knowledge Graphs",
    description: "Visualize concept relationships",
    icon: Globe,
    color: "emerald",
    estimatedTime: "15-20 min"
  },
  practicalScenarios: {
    name: "Practical Scenarios",
    description: "Apply knowledge in real situations",
    icon: Users,
    color: "bright-green",
    estimatedTime: "20-25 min"
  },
  matchConcepts: {
    name: "Match Concepts",
    description: "Connect related ideas",
    icon: Layers,
    color: "lime-green",
    estimatedTime: "10-15 min"
  },
  conceptsGuidedLearning: {
    name: "Guided Learning",
    description: "Step-by-step concept exploration",
    icon: Lightbulb,
    color: "emerald",
    estimatedTime: "25-30 min"
  }
};

export default function LearningLesson({ 
  plan, 
  onPlanUpdate, 
  onComplete 
}: LearningLessonProps) {
  const { user } = useUser();
  const locale = useLocale();
  
  const [currentTool, setCurrentTool] = useState<string | null>(null);
  const [toolSessions, setToolSessions] = useState<Record<string, ToolSession>>({});
  const [isGenerating, setIsGenerating] = useState(false);
  const [sessionStartTime] = useState(Date.now());
  const [totalTimeSpent, setTotalTimeSpent] = useState(0);

  // Calculate overall progress
  const enabledTools = Object.entries(plan.tools).filter(([_, enabled]) => enabled).map(([tool]) => tool);
  const completedTools = plan.progress.completedTools || [];
  const overallProgress = enabledTools.length > 0 ? (completedTools.length / enabledTools.length) * 100 : 0;
  const averageScore = Object.values(toolSessions)
    .filter(session => session.progress.completed && session.progress.score !== undefined)
    .reduce((sum, session, _, arr) => sum + (session.progress.score || 0) / arr.length, 0);

  // Generate tool content
  const generateToolContent = useCallback(async (toolType: string) => {
    if (!user) return;
    
    setIsGenerating(true);
    try {
      const response = await fetch('/api/ai/learning/tools', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          planId: plan.id,
          toolType,
          userId: user.uid,
          difficulty: plan.difficulty?.toLowerCase(),
          count: toolType === 'flashcards' ? 12 : toolType === 'trueOrFalse' ? 10 : 6
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate content');
      }

      const data = await response.json();
      
      setToolSessions(prev => ({
        ...prev,
        [toolType]: {
          id: data.sessionId,
          toolType,
          content: data.content,
          progress: {
            completed: false,
            timeSpent: 0,
            attempts: 0,
          }
        }
      }));

      setCurrentTool(toolType);
      toast.success(`${TOOL_CONFIGS[toolType as keyof typeof TOOL_CONFIGS]?.name} content generated!`);
      
    } catch (error) {
      console.error('Error generating tool content:', error);
      toast.error('Failed to generate content. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  }, [user, plan.id, plan.difficulty]);

  // Handle tool completion
  const handleToolComplete = useCallback((toolType: string, progress: any) => {
    setToolSessions(prev => ({
      ...prev,
      [toolType]: {
        ...prev[toolType],
        progress: {
          ...prev[toolType].progress,
          ...progress
        }
      }
    }));

    // Update plan progress
    const newCompletedTools = [...(plan.progress.completedTools || [])];
    if (!newCompletedTools.includes(toolType)) {
      newCompletedTools.push(toolType);
    }

    const updatedProgress = {
      ...plan.progress,
      completedTools: newCompletedTools,
      timeSpent: (plan.progress.timeSpent || 0) + (progress.timeSpent || 0),
      lastAccessedAt: new Date(),
    };

    onPlanUpdate?.({ progress: updatedProgress });

    // Check if all tools are completed
    if (newCompletedTools.length === enabledTools.length) {
      toast.success("Congratulations! You've completed all learning tools!");
      onComplete?.();
    }

    setCurrentTool(null);
  }, [plan.progress, enabledTools.length, onPlanUpdate, onComplete]);

  // Render current tool
  const renderCurrentTool = () => {
    if (!currentTool || !toolSessions[currentTool]) return null;

    const session = toolSessions[currentTool];
    const toolConfig = TOOL_CONFIGS[currentTool as keyof typeof TOOL_CONFIGS];

    const commonProps = {
      planId: plan.id,
      sessionId: session.id,
      onProgress: (progress: any) => handleToolComplete(currentTool, progress),
      onComplete: () => setCurrentTool(null)
    };

    switch (currentTool) {
      case 'flashcards':
        return <Flashcards cards={session.content.cards} {...commonProps} />;
      case 'trueOrFalse':
        return <TrueOrFalse questions={session.content.questions} {...commonProps} />;
      case 'twoFactsOneLie':
        return <TwoFactsOneLie rounds={session.content.rounds} {...commonProps} />;
      case 'knowledgeGraphs':
        return <KnowledgeGraphs nodes={session.content.nodes} edges={session.content.edges} {...commonProps} />;
      case 'practicalScenarios':
        return <PracticalScenarioGuidance scenarios={session.content.scenarios} {...commonProps} />;
      case 'matchConcepts':
        return <MatchConcepts exercises={session.content.exercises} {...commonProps} />;
      case 'conceptsGuidedLearning':
        return <ConceptsGuidedLearning modules={session.content.modules} {...commonProps} />;
      default:
        return (
          <div className="text-center py-12">
            <div className="p-6 bg-bright-green/10 rounded-full w-fit mx-auto mb-6">
              <toolConfig.icon className="h-12 w-12 text-bright-green" />
            </div>
            <h3 className="text-2xl font-bold text-charcoal mb-4">{toolConfig.name}</h3>
            <p className="text-grey mb-8">{toolConfig.description}</p>
            <p className="text-grey">This tool is coming soon!</p>
          </div>
        );
    }
  };

  if (currentTool) {
    return (
      <div className="w-full">
        {/* Tool Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <Button
              onClick={() => setCurrentTool(null)}
              variant="outline"
              className="border-emerald/30 text-emerald hover:bg-emerald/10"
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Back to Tools
            </Button>
            
            <div className="text-center">
              <h2 className="text-2xl font-bold text-charcoal">{plan.title}</h2>
              <p className="text-grey">{TOOL_CONFIGS[currentTool as keyof typeof TOOL_CONFIGS]?.name}</p>
            </div>
            
            <div className="w-24" /> {/* Spacer */}
          </div>
        </div>

        {/* Tool Content */}
        {renderCurrentTool()}
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-8">
      {/* Hero Header */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald/10 via-bright-green/10 to-lime-green/10 rounded-3xl blur-xl" />
        <div className="relative bg-white/80 backdrop-blur-lg rounded-3xl border border-white/20 p-12 shadow-2xl">
          <div className="text-center space-y-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <h1 className="text-5xl font-bold bg-gradient-to-r from-emerald via-bright-green to-lime-green bg-clip-text text-transparent">
                {plan.title}
              </h1>
              <p className="text-xl text-grey max-w-3xl mx-auto leading-relaxed">
                {plan.description}
              </p>
            </motion.div>
            
            {/* Plan Stats */}
            <div className="flex flex-wrap items-center justify-center gap-6 mt-8">
              <div className="flex items-center gap-2 px-6 py-3 bg-emerald/10 rounded-full">
                <Target className="h-5 w-5 text-emerald" />
                <span className="font-medium text-emerald">
                  {Math.round(overallProgress)}% Complete
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-6 py-3 bg-bright-green/10 rounded-full">
                <TrendingUp className="h-5 w-5 text-bright-green" />
                <span className="font-medium text-bright-green">
                  {Math.round(averageScore || 0)}% Avg Score
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-6 py-3 bg-lime-green/10 rounded-full">
                <Clock className="h-5 w-5 text-lime-green" />
                <span className="font-medium text-lime-green">
                  {plan.estimatedDuration}
                </span>
              </div>
              
              <Badge className="px-6 py-3 text-base bg-charcoal text-white">
                {plan.difficulty}
              </Badge>
            </div>
            
            {/* Progress Bar */}
            <div className="max-w-2xl mx-auto space-y-2">
              <div className="flex justify-between text-sm text-grey">
                <span>Learning Progress</span>
                <span>{completedTools.length} of {enabledTools.length} tools completed</span>
              </div>
              <Progress 
                value={overallProgress} 
                className="h-4 bg-grey/20"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Learning Objectives */}
      {plan.objectives && plan.objectives.length > 0 && (
        <div className="bg-gradient-to-r from-emerald/5 to-bright-green/5 rounded-2xl p-8 border border-emerald/20">
          <div className="flex items-start gap-4">
            <Star className="h-6 w-6 text-emerald mt-1 flex-shrink-0" />
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-charcoal">Learning Objectives</h3>
              <ul className="space-y-2">
                {plan.objectives.map((objective, index) => (
                  <li key={index} className="flex items-start gap-3 text-grey">
                    <CheckCircle className="h-4 w-4 text-emerald mt-1 flex-shrink-0" />
                    <span>{objective}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Learning Tools Grid */}
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-charcoal mb-4">Choose Your Learning Tool</h2>
          <p className="text-grey text-lg">Each tool offers a unique way to master the concepts</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {enabledTools.map((toolType) => {
            const config = TOOL_CONFIGS[toolType as keyof typeof TOOL_CONFIGS];
            const isCompleted = completedTools.includes(toolType);
            const session = toolSessions[toolType];
            
            if (!config) return null;
            
            const colorClasses = {
              emerald: "from-emerald/10 to-emerald/5 border-emerald/20 hover:border-emerald/40",
              "bright-green": "from-bright-green/10 to-bright-green/5 border-bright-green/20 hover:border-bright-green/40",
              "lime-green": "from-lime-green/10 to-lime-green/5 border-lime-green/20 hover:border-lime-green/40"
            };
            
            const iconColorClasses = {
              emerald: "text-emerald bg-emerald/10",
              "bright-green": "text-bright-green bg-bright-green/10",
              "lime-green": "text-lime-green bg-lime-green/10"
            };
            
            return (
              <motion.div
                key={toolType}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: enabledTools.indexOf(toolType) * 0.1 }}
              >
                <Card className={cn(
                  "relative cursor-pointer transition-all duration-300 hover:shadow-xl",
                  "bg-gradient-to-br border-2 h-full",
                  colorClasses[config.color as keyof typeof colorClasses],
                  isCompleted && "ring-2 ring-emerald/30"
                )}>
                  <CardContent className="p-8 h-full flex flex-col">
                    {/* Completion Badge */}
                    {isCompleted && (
                      <div className="absolute top-4 right-4">
                        <CheckCircle className="h-6 w-6 text-emerald" />
                      </div>
                    )}
                    
                    {/* Tool Icon */}
                    <div className={cn(
                      "p-4 rounded-2xl w-fit mb-6",
                      iconColorClasses[config.color as keyof typeof iconColorClasses]
                    )}>
                      <config.icon className="h-8 w-8" />
                    </div>
                    
                    {/* Tool Info */}
                    <div className="flex-1 space-y-4">
                      <div>
                        <h3 className="text-xl font-bold text-charcoal mb-2">{config.name}</h3>
                        <p className="text-grey leading-relaxed">{config.description}</p>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm text-grey">
                          <Clock className="h-4 w-4" />
                          <span>{config.estimatedTime}</span>
                        </div>
                        
                        {session?.progress.completed && (
                          <div className="flex items-center gap-2 text-sm text-emerald">
                            <Award className="h-4 w-4" />
                            <span>Score: {session.progress.score || 0}%</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Action Button */}
                    <Button
                      onClick={() => generateToolContent(toolType)}
                      disabled={isGenerating}
                      className={cn(
                        "w-full mt-6",
                        config.color === "emerald" && "bg-emerald hover:bg-emerald-deep",
                        config.color === "bright-green" && "bg-bright-green hover:bg-bright-green/90",
                        config.color === "lime-green" && "bg-lime-green hover:bg-lime-green/90",
                        "text-white"
                      )}
                    >
                      {isGenerating ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <Play className="h-4 w-4 mr-2" />
                          {isCompleted ? "Practice Again" : "Start Learning"}
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
