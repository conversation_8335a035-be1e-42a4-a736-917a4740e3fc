"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/Card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  Target, 
  Brain,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Lightbulb,
  ChevronRight,
  ChevronLeft,
  RotateCcw,
  Award,
  TrendingUp,
  BookOpen,
  Zap,
  Eye,
  ThumbsUp,
  ThumbsDown
} from "lucide-react";
import { toast } from "sonner";

interface ScenarioOption {
  id: string;
  action: string;
  outcome: string;
  isOptimal: boolean;
  reasoning: string;
}

interface PracticalScenario {
  id: string;
  title: string;
  context: string;
  situation: string;
  options: ScenarioOption[];
  learningPoints: string[];
  difficulty: "easy" | "medium" | "hard";
}

interface PracticalScenarioGuidanceProps {
  scenarios: PracticalScenario[];
  planId: string;
  sessionId: string;
  onProgress?: (progress: any) => void;
  onComplete?: () => void;
}

interface ScenarioResult {
  scenarioId: string;
  selectedOptionId: string;
  isOptimal: boolean;
  timeSpent: number;
}

export default function PracticalScenarioGuidance({ 
  scenarios, 
  planId, 
  sessionId, 
  onProgress, 
  onComplete 
}: PracticalScenarioGuidanceProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [showResults, setShowResults] = useState(false);
  const [results, setResults] = useState<ScenarioResult[]>([]);
  const [sessionStartTime] = useState(Date.now());
  const [scenarioStartTime, setScenarioStartTime] = useState(Date.now());
  const [isComplete, setIsComplete] = useState(false);
  const [optimalChoices, setOptimalChoices] = useState(0);

  const currentScenario = scenarios[currentIndex];
  const progress = ((currentIndex + (showResults ? 1 : 0)) / scenarios.length) * 100;
  const accuracy = results.length > 0 ? (optimalChoices / results.length) * 100 : 0;

  // Handle option selection
  const handleOptionSelect = useCallback((optionId: string) => {
    if (selectedOption) return;
    
    const timeSpent = Date.now() - scenarioStartTime;
    const option = currentScenario.options.find(opt => opt.id === optionId);
    
    if (!option) return;
    
    setSelectedOption(optionId);
    setShowResults(true);
    
    const result: ScenarioResult = {
      scenarioId: currentScenario.id,
      selectedOptionId: optionId,
      isOptimal: option.isOptimal,
      timeSpent,
    };
    
    setResults(prev => [...prev, result]);
    
    if (option.isOptimal) {
      setOptimalChoices(prev => prev + 1);
      toast.success("Excellent choice! Well reasoned.");
    } else {
      toast.error("Not the optimal choice. Learn from the feedback!");
    }
  }, [selectedOption, scenarioStartTime, currentScenario]);

  // Move to next scenario
  const nextScenario = useCallback(() => {
    if (currentIndex < scenarios.length - 1) {
      setCurrentIndex(prev => prev + 1);
      setSelectedOption(null);
      setShowResults(false);
      setScenarioStartTime(Date.now());
    } else {
      // Complete session
      setIsComplete(true);
      const totalTime = Math.round((Date.now() - sessionStartTime) / 1000 / 60);
      const finalAccuracy = Math.round(accuracy);
      
      onProgress?.({
        completed: true,
        score: finalAccuracy,
        timeSpent: totalTime,
        attempts: 1,
        details: {
          totalScenarios: scenarios.length,
          optimalChoices,
          accuracy: finalAccuracy,
          averageTimePerScenario: Math.round(results.reduce((sum, r) => sum + r.timeSpent, 0) / results.length / 1000),
        }
      });
      
      onComplete?.();
    }
  }, [currentIndex, scenarios.length, sessionStartTime, accuracy, optimalChoices, results, onProgress, onComplete]);

  // Previous scenario (for review)
  const previousScenario = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
      setSelectedOption(null);
      setShowResults(false);
      setScenarioStartTime(Date.now());
    }
  }, [currentIndex]);

  // Restart session
  const restartSession = useCallback(() => {
    setCurrentIndex(0);
    setSelectedOption(null);
    setShowResults(false);
    setResults([]);
    setIsComplete(false);
    setOptimalChoices(0);
    setScenarioStartTime(Date.now());
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (selectedOption) return;
      
      const optionIndex = parseInt(e.key) - 1;
      if (optionIndex >= 0 && optionIndex < currentScenario.options.length) {
        e.preventDefault();
        handleOptionSelect(currentScenario.options[optionIndex].id);
      }
      
      if (e.key === "Enter" && showResults) {
        e.preventDefault();
        nextScenario();
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [selectedOption, showResults, currentScenario, handleOptionSelect, nextScenario]);

  if (!currentScenario) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <BookOpen className="h-12 w-12 text-grey mx-auto mb-4" />
          <p className="text-grey">No scenarios available</p>
        </div>
      </div>
    );
  }

  const difficultyColors = {
    easy: "bg-lime-green/20 text-lime-green border-lime-green/30",
    medium: "bg-bright-green/20 text-bright-green border-bright-green/30",
    hard: "bg-emerald/20 text-emerald border-emerald/30"
  };

  if (isComplete) {
    return (
      <div className="w-full max-w-4xl mx-auto p-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center space-y-8"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald/20 via-bright-green/20 to-lime-green/20 rounded-3xl blur-xl" />
            <div className="relative bg-white/90 backdrop-blur-lg rounded-3xl border border-white/20 p-12 shadow-2xl">
              <Award className="h-16 w-16 text-emerald mx-auto mb-6" />
              <h2 className="text-4xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent mb-4">
                Scenarios Complete!
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                <div className="bg-emerald/10 rounded-2xl p-6">
                  <Target className="h-8 w-8 text-emerald mx-auto mb-2" />
                  <div className="text-3xl font-bold text-emerald">{Math.round(accuracy)}%</div>
                  <div className="text-sm text-grey">Decision Quality</div>
                </div>
                
                <div className="bg-bright-green/10 rounded-2xl p-6">
                  <TrendingUp className="h-8 w-8 text-bright-green mx-auto mb-2" />
                  <div className="text-3xl font-bold text-bright-green">{optimalChoices}</div>
                  <div className="text-sm text-grey">Optimal Choices</div>
                </div>
                
                <div className="bg-lime-green/10 rounded-2xl p-6">
                  <Clock className="h-8 w-8 text-lime-green mx-auto mb-2" />
                  <div className="text-3xl font-bold text-lime-green">
                    {Math.round((Date.now() - sessionStartTime) / 1000 / 60)}m
                  </div>
                  <div className="text-sm text-grey">Time Spent</div>
                </div>
              </div>
              
              <div className="flex justify-center gap-4 mt-8">
                <Button
                  onClick={restartSession}
                  className="bg-emerald hover:bg-emerald-deep text-white"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Practice Again
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-8">
      {/* Header with Stats */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald/10 via-bright-green/10 to-lime-green/10 rounded-3xl blur-xl" />
        <div className="relative bg-white/80 backdrop-blur-lg rounded-3xl border border-white/20 p-8 shadow-2xl">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent">
                Practical Scenario Guidance
              </h2>
              <p className="text-grey text-lg">Apply knowledge in real-world situations</p>
            </div>
            
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center gap-2 px-4 py-2 bg-emerald/10 rounded-full">
                <Target className="h-4 w-4 text-emerald" />
                <span className="text-sm font-medium text-emerald">
                  {Math.round(accuracy)}% Optimal
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-bright-green/10 rounded-full">
                <TrendingUp className="h-4 w-4 text-bright-green" />
                <span className="text-sm font-medium text-bright-green">
                  {optimalChoices} Good Choices
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-lime-green/10 rounded-full">
                <Brain className="h-4 w-4 text-lime-green" />
                <span className="text-sm font-medium text-lime-green">
                  {currentIndex + 1}/{scenarios.length}
                </span>
              </div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-6 space-y-2">
            <div className="flex justify-between text-sm text-grey">
              <span>Scenario Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress 
              value={progress} 
              className="h-3 bg-grey/20"
            />
          </div>
        </div>
      </div>

      {/* Scenario Card */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-emerald/5 rounded-3xl blur-sm" />
        <Card className="relative bg-white/90 backdrop-blur-lg border-2 border-emerald/20 shadow-2xl rounded-3xl overflow-hidden">
          <CardContent className="p-12">
            <div className="space-y-8">
              {/* Scenario Header */}
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center gap-4 mb-6">
                  <Badge className={cn("px-4 py-2", difficultyColors[currentScenario.difficulty])}>
                    {currentScenario.difficulty.toUpperCase()}
                  </Badge>
                  <Badge variant="outline" className="px-4 py-2 border-grey/30 text-grey">
                    Scenario {currentIndex + 1}
                  </Badge>
                </div>
                
                <h3 className="text-3xl font-bold text-charcoal mb-4">
                  {currentScenario.title}
                </h3>
              </div>
              
              {/* Context and Situation */}
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-emerald/5 to-bright-green/5 rounded-2xl p-6">
                  <h4 className="text-lg font-semibold text-charcoal mb-3 flex items-center gap-2">
                    <Eye className="h-5 w-5 text-emerald" />
                    Context
                  </h4>
                  <p className="text-grey leading-relaxed">{currentScenario.context}</p>
                </div>
                
                <div className="bg-gradient-to-r from-bright-green/5 to-lime-green/5 rounded-2xl p-6">
                  <h4 className="text-lg font-semibold text-charcoal mb-3 flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-bright-green" />
                    Situation
                  </h4>
                  <p className="text-grey leading-relaxed">{currentScenario.situation}</p>
                </div>
              </div>
              
              {/* Options */}
              <div className="space-y-4">
                <h4 className="text-xl font-semibold text-charcoal text-center mb-6">
                  What would you do?
                </h4>
                
                <div className="grid gap-4">
                  {currentScenario.options.map((option, index) => {
                    const isSelected = selectedOption === option.id;
                    const showResult = showResults && isSelected;
                    
                    return (
                      <motion.div
                        key={option.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Card 
                          className={cn(
                            "cursor-pointer transition-all duration-300 hover:shadow-lg",
                            "border-2 bg-white/90 backdrop-blur-sm",
                            !selectedOption && "hover:border-emerald/40 hover:bg-emerald/5",
                            isSelected && !showResult && "border-emerald bg-emerald/10",
                            showResult && option.isOptimal && "border-emerald bg-emerald/10",
                            showResult && !option.isOptimal && "border-red-400 bg-red-50"
                          )}
                          onClick={() => !selectedOption && handleOptionSelect(option.id)}
                        >
                          <CardContent className="p-6">
                            <div className="flex items-start gap-4">
                              <div className={cn(
                                "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center text-lg font-bold",
                                !selectedOption && "bg-grey/10 text-grey",
                                isSelected && !showResult && "bg-emerald text-white",
                                showResult && option.isOptimal && "bg-emerald text-white",
                                showResult && !option.isOptimal && "bg-red-500 text-white"
                              )}>
                                {index + 1}
                              </div>
                              
                              <div className="flex-1 space-y-3">
                                <p className="text-lg text-charcoal leading-relaxed font-medium">
                                  {option.action}
                                </p>
                                
                                {showResult && (
                                  <div className="space-y-3">
                                    <div className="flex items-center gap-2">
                                      {option.isOptimal ? (
                                        <>
                                          <ThumbsUp className="h-5 w-5 text-emerald" />
                                          <span className="text-emerald font-medium">Optimal Choice</span>
                                        </>
                                      ) : (
                                        <>
                                          <ThumbsDown className="h-5 w-5 text-red-500" />
                                          <span className="text-red-600 font-medium">Not Optimal</span>
                                        </>
                                      )}
                                    </div>
                                    
                                    <div className="bg-grey/5 rounded-lg p-4 space-y-2">
                                      <h5 className="font-medium text-charcoal">Outcome:</h5>
                                      <p className="text-grey text-sm">{option.outcome}</p>
                                    </div>
                                    
                                    <div className="bg-grey/5 rounded-lg p-4 space-y-2">
                                      <h5 className="font-medium text-charcoal">Reasoning:</h5>
                                      <p className="text-grey text-sm">{option.reasoning}</p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Learning Points */}
      <AnimatePresence>
        {showResults && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-gradient-to-r from-bright-green/10 to-lime-green/10 rounded-2xl p-8 border border-bright-green/20"
          >
            <div className="flex items-start gap-4">
              <Lightbulb className="h-6 w-6 text-bright-green mt-1 flex-shrink-0" />
              <div className="space-y-4">
                <h4 className="text-xl font-semibold text-charcoal">Key Learning Points:</h4>
                <ul className="space-y-2">
                  {currentScenario.learningPoints.map((point, index) => (
                    <li key={index} className="flex items-start gap-3 text-grey">
                      <CheckCircle className="h-4 w-4 text-bright-green mt-1 flex-shrink-0" />
                      <span>{point}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          onClick={previousScenario}
          disabled={currentIndex === 0}
          variant="outline"
          className="border-emerald/30 text-emerald hover:bg-emerald/10 disabled:opacity-50"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <div className="text-center">
          <p className="text-sm text-grey">
            Press <kbd className="px-2 py-1 bg-grey/10 rounded text-xs">1-{currentScenario.options.length}</kbd> to select option
          </p>
        </div>
        
        {showResults && (
          <Button
            onClick={nextScenario}
            className="bg-emerald hover:bg-emerald-deep text-white"
          >
            {currentIndex === scenarios.length - 1 ? "Complete" : "Next Scenario"}
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        )}
        
        {!showResults && (
          <div className="w-32" /> // Spacer to center the keyboard hint
        )}
      </div>
    </div>
  );
}
