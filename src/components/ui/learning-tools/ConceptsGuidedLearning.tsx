"use client";

import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/Card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Lightbulb, 
  Target, 
  Brain,
  Clock,
  CheckCircle,
  XCircle,
  ChevronRight,
  ChevronLeft,
  Award,
  TrendingUp,
  BookOpen,
  Eye,
  MessageSquare,
  HelpCircle,
  Zap,
  RotateCcw,
  Play,
  Pause
} from "lucide-react";
import { toast } from "sonner";

interface InteractiveElement {
  type: "question" | "exercise" | "visualization";
  content: string;
  answer?: string;
}

interface LearningStep {
  id: string;
  title: string;
  content: string;
  type: "explanation" | "example" | "practice" | "reflection";
  interactiveElements?: InteractiveElement[];
}

interface AssessmentQuestion {
  question: string;
  options: string[];
  correct: number;
  explanation: string;
}

interface LearningModule {
  id: string;
  title: string;
  description: string;
  steps: LearningStep[];
  assessment: {
    questions: AssessmentQuestion[];
  };
  difficulty: "easy" | "medium" | "hard";
}

interface ConceptsGuidedLearningProps {
  modules: LearningModule[];
  planId: string;
  sessionId: string;
  onProgress?: (progress: any) => void;
  onComplete?: () => void;
}

interface ModuleResult {
  moduleId: string;
  stepsCompleted: number;
  assessmentScore: number;
  timeSpent: number;
}

export default function ConceptsGuidedLearning({ 
  modules, 
  planId, 
  sessionId, 
  onProgress, 
  onComplete 
}: ConceptsGuidedLearningProps) {
  const [currentModuleIndex, setCurrentModuleIndex] = useState(0);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const [showAssessment, setShowAssessment] = useState(false);
  const [assessmentAnswers, setAssessmentAnswers] = useState<number[]>([]);
  const [showAssessmentResults, setShowAssessmentResults] = useState(false);
  const [results, setResults] = useState<ModuleResult[]>([]);
  const [sessionStartTime] = useState(Date.now());
  const [moduleStartTime, setModuleStartTime] = useState(Date.now());
  const [isComplete, setIsComplete] = useState(false);
  const [interactiveAnswers, setInteractiveAnswers] = useState<Record<string, string>>({});

  const currentModule = modules[currentModuleIndex];
  const currentStep = currentModule?.steps[currentStepIndex];
  const totalSteps = modules.reduce((sum, module) => sum + module.steps.length, 0);
  const completedStepsCount = results.reduce((sum, result) => sum + result.stepsCompleted, 0) + completedSteps.size;
  const progress = totalSteps > 0 ? (completedStepsCount / totalSteps) * 100 : 0;

  // Handle step completion
  const completeStep = useCallback(() => {
    if (!currentStep) return;
    
    setCompletedSteps(prev => new Set([...prev, currentStep.id]));
    
    if (currentStepIndex < currentModule.steps.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
    } else {
      // Module steps completed, show assessment
      setShowAssessment(true);
    }
  }, [currentStep, currentStepIndex, currentModule]);

  // Handle assessment submission
  const submitAssessment = useCallback(() => {
    if (assessmentAnswers.length !== currentModule.assessment.questions.length) {
      toast.error("Please answer all questions before submitting");
      return;
    }

    const correctAnswers = assessmentAnswers.filter(
      (answer, index) => answer === currentModule.assessment.questions[index].correct
    ).length;
    
    const score = Math.round((correctAnswers / currentModule.assessment.questions.length) * 100);
    const timeSpent = Date.now() - moduleStartTime;

    const result: ModuleResult = {
      moduleId: currentModule.id,
      stepsCompleted: completedSteps.size,
      assessmentScore: score,
      timeSpent,
    };

    setResults(prev => [...prev, result]);
    setShowAssessmentResults(true);

    if (score >= 70) {
      toast.success(`Great job! You scored ${score}%`);
    } else {
      toast.error(`Score: ${score}%. Review the material and try again.`);
    }
  }, [assessmentAnswers, currentModule, completedSteps.size, moduleStartTime]);

  // Move to next module
  const nextModule = useCallback(() => {
    if (currentModuleIndex < modules.length - 1) {
      setCurrentModuleIndex(prev => prev + 1);
      setCurrentStepIndex(0);
      setCompletedSteps(new Set());
      setShowAssessment(false);
      setAssessmentAnswers([]);
      setShowAssessmentResults(false);
      setModuleStartTime(Date.now());
    } else {
      // Complete session
      setIsComplete(true);
      const totalTime = Math.round((Date.now() - sessionStartTime) / 1000 / 60);
      const averageScore = results.length > 0 
        ? Math.round(results.reduce((sum, r) => sum + r.assessmentScore, 0) / results.length)
        : 0;
      
      onProgress?.({
        completed: true,
        score: averageScore,
        timeSpent: totalTime,
        attempts: 1,
        details: {
          totalModules: modules.length,
          completedModules: results.length,
          averageScore,
          totalStepsCompleted: completedStepsCount,
        }
      });
      
      onComplete?.();
    }
  }, [currentModuleIndex, modules.length, sessionStartTime, results, completedStepsCount, onProgress, onComplete]);

  // Previous step
  const previousStep = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prev => prev - 1);
    }
  }, [currentStepIndex]);

  // Next step
  const nextStep = useCallback(() => {
    if (currentStepIndex < currentModule.steps.length - 1) {
      setCurrentStepIndex(prev => prev + 1);
    }
  }, [currentStepIndex, currentModule]);

  // Restart session
  const restartSession = useCallback(() => {
    setCurrentModuleIndex(0);
    setCurrentStepIndex(0);
    setCompletedSteps(new Set());
    setShowAssessment(false);
    setAssessmentAnswers([]);
    setShowAssessmentResults(false);
    setResults([]);
    setIsComplete(false);
    setInteractiveAnswers({});
    setModuleStartTime(Date.now());
  }, []);

  if (!currentModule) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <BookOpen className="h-12 w-12 text-grey mx-auto mb-4" />
          <p className="text-grey">No learning modules available</p>
        </div>
      </div>
    );
  }

  const difficultyColors = {
    easy: "bg-lime-green/20 text-lime-green border-lime-green/30",
    medium: "bg-bright-green/20 text-bright-green border-bright-green/30",
    hard: "bg-emerald/20 text-emerald border-emerald/30"
  };

  const stepTypeIcons = {
    explanation: BookOpen,
    example: Eye,
    practice: Zap,
    reflection: MessageSquare
  };

  const stepTypeColors = {
    explanation: "emerald",
    example: "bright-green",
    practice: "lime-green",
    reflection: "charcoal"
  };

  if (isComplete) {
    const averageScore = results.length > 0 
      ? Math.round(results.reduce((sum, r) => sum + r.assessmentScore, 0) / results.length)
      : 0;

    return (
      <div className="w-full max-w-4xl mx-auto p-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center space-y-8"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald/20 via-bright-green/20 to-lime-green/20 rounded-3xl blur-xl" />
            <div className="relative bg-white/90 backdrop-blur-lg rounded-3xl border border-white/20 p-12 shadow-2xl">
              <Award className="h-16 w-16 text-emerald mx-auto mb-6" />
              <h2 className="text-4xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent mb-4">
                Learning Journey Complete!
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                <div className="bg-emerald/10 rounded-2xl p-6">
                  <Target className="h-8 w-8 text-emerald mx-auto mb-2" />
                  <div className="text-3xl font-bold text-emerald">{averageScore}%</div>
                  <div className="text-sm text-grey">Average Score</div>
                </div>
                
                <div className="bg-bright-green/10 rounded-2xl p-6">
                  <TrendingUp className="h-8 w-8 text-bright-green mx-auto mb-2" />
                  <div className="text-3xl font-bold text-bright-green">{results.length}</div>
                  <div className="text-sm text-grey">Modules Completed</div>
                </div>
                
                <div className="bg-lime-green/10 rounded-2xl p-6">
                  <Clock className="h-8 w-8 text-lime-green mx-auto mb-2" />
                  <div className="text-3xl font-bold text-lime-green">
                    {Math.round((Date.now() - sessionStartTime) / 1000 / 60)}m
                  </div>
                  <div className="text-sm text-grey">Total Time</div>
                </div>
              </div>
              
              <div className="flex justify-center gap-4 mt-8">
                <Button
                  onClick={restartSession}
                  className="bg-emerald hover:bg-emerald-deep text-white"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Start Over
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  if (showAssessment) {
    return (
      <div className="w-full max-w-4xl mx-auto p-6 space-y-8">
        {/* Assessment Header */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-emerald/10 via-bright-green/10 to-lime-green/10 rounded-3xl blur-xl" />
          <div className="relative bg-white/80 backdrop-blur-lg rounded-3xl border border-white/20 p-8 shadow-2xl">
            <div className="text-center space-y-4">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent">
                Module Assessment
              </h2>
              <p className="text-grey text-lg">{currentModule.title}</p>
              <Badge className={cn("px-4 py-2", difficultyColors[currentModule.difficulty])}>
                {currentModule.difficulty.toUpperCase()}
              </Badge>
            </div>
          </div>
        </div>

        {/* Assessment Questions */}
        <div className="space-y-6">
          {currentModule.assessment.questions.map((question, questionIndex) => (
            <Card key={questionIndex} className="bg-white/90 backdrop-blur-sm border-2 border-emerald/20">
              <CardContent className="p-8">
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold text-charcoal">
                    {questionIndex + 1}. {question.question}
                  </h3>
                  
                  <div className="space-y-3">
                    {question.options.map((option, optionIndex) => (
                      <div
                        key={optionIndex}
                        className={cn(
                          "p-4 rounded-lg border-2 cursor-pointer transition-all duration-200",
                          assessmentAnswers[questionIndex] === optionIndex
                            ? "border-emerald bg-emerald/10"
                            : "border-grey/20 bg-white hover:border-emerald/40 hover:bg-emerald/5"
                        )}
                        onClick={() => {
                          const newAnswers = [...assessmentAnswers];
                          newAnswers[questionIndex] = optionIndex;
                          setAssessmentAnswers(newAnswers);
                        }}
                      >
                        <div className="flex items-center gap-3">
                          <div className={cn(
                            "w-6 h-6 rounded-full border-2 flex items-center justify-center",
                            assessmentAnswers[questionIndex] === optionIndex
                              ? "border-emerald bg-emerald text-white"
                              : "border-grey/30"
                          )}>
                            {assessmentAnswers[questionIndex] === optionIndex && (
                              <CheckCircle className="h-4 w-4" />
                            )}
                          </div>
                          <span className="text-charcoal">{option}</span>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Show explanation if results are visible */}
                  {showAssessmentResults && (
                    <div className={cn(
                      "p-4 rounded-lg border-2",
                      assessmentAnswers[questionIndex] === question.correct
                        ? "border-emerald bg-emerald/10"
                        : "border-red-400 bg-red-50"
                    )}>
                      <div className="flex items-start gap-3">
                        {assessmentAnswers[questionIndex] === question.correct ? (
                          <CheckCircle className="h-5 w-5 text-emerald mt-1" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-500 mt-1" />
                        )}
                        <div>
                          <p className="font-medium text-charcoal mb-2">
                            {assessmentAnswers[questionIndex] === question.correct ? "Correct!" : "Incorrect"}
                          </p>
                          <p className="text-grey text-sm">{question.explanation}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Assessment Actions */}
        <div className="flex items-center justify-center gap-4">
          {!showAssessmentResults ? (
            <Button
              onClick={submitAssessment}
              disabled={assessmentAnswers.length !== currentModule.assessment.questions.length}
              className="bg-emerald hover:bg-emerald-deep text-white px-8 py-3"
            >
              <CheckCircle className="h-5 w-5 mr-2" />
              Submit Assessment
            </Button>
          ) : (
            <Button
              onClick={nextModule}
              className="bg-emerald hover:bg-emerald-deep text-white px-8 py-3"
            >
              {currentModuleIndex === modules.length - 1 ? "Complete Learning" : "Next Module"}
              <ChevronRight className="h-5 w-5 ml-2" />
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-8">
      {/* Header with Stats */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald/10 via-bright-green/10 to-lime-green/10 rounded-3xl blur-xl" />
        <div className="relative bg-white/80 backdrop-blur-lg rounded-3xl border border-white/20 p-8 shadow-2xl">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent">
                Guided Learning
              </h2>
              <p className="text-grey text-lg">Step-by-step concept exploration</p>
            </div>
            
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center gap-2 px-4 py-2 bg-emerald/10 rounded-full">
                <Target className="h-4 w-4 text-emerald" />
                <span className="text-sm font-medium text-emerald">
                  {Math.round(progress)}% Complete
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-bright-green/10 rounded-full">
                <Brain className="h-4 w-4 text-bright-green" />
                <span className="text-sm font-medium text-bright-green">
                  Module {currentModuleIndex + 1}/{modules.length}
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-lime-green/10 rounded-full">
                <Lightbulb className="h-4 w-4 text-lime-green" />
                <span className="text-sm font-medium text-lime-green">
                  Step {currentStepIndex + 1}/{currentModule.steps.length}
                </span>
              </div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-6 space-y-2">
            <div className="flex justify-between text-sm text-grey">
              <span>Overall Progress</span>
              <span>{completedStepsCount}/{totalSteps} steps</span>
            </div>
            <Progress 
              value={progress} 
              className="h-3 bg-grey/20"
            />
          </div>
        </div>
      </div>

      {/* Module Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-4">
          <Badge className={cn("px-4 py-2", difficultyColors[currentModule.difficulty])}>
            {currentModule.difficulty.toUpperCase()}
          </Badge>
          <Badge variant="outline" className="px-4 py-2 border-grey/30 text-grey">
            {currentModule.title}
          </Badge>
        </div>
        <p className="text-grey max-w-2xl mx-auto">{currentModule.description}</p>
      </div>

      {/* Current Step */}
      {currentStep && (
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-emerald/5 rounded-3xl blur-sm" />
          <Card className="relative bg-white/90 backdrop-blur-lg border-2 border-emerald/20 shadow-2xl rounded-3xl overflow-hidden">
            <CardContent className="p-12">
              <div className="space-y-8">
                {/* Step Header */}
                <div className="text-center space-y-4">
                  <div className="flex items-center justify-center gap-4">
                    <div className={cn(
                      "p-3 rounded-full",
                      `bg-${stepTypeColors[currentStep.type]}/10`
                    )}>
                      {React.createElement(stepTypeIcons[currentStep.type], {
                        className: `h-6 w-6 text-${stepTypeColors[currentStep.type]}`
                      })}
                    </div>
                    <Badge variant="outline" className="px-4 py-2 border-grey/30 text-grey capitalize">
                      {currentStep.type}
                    </Badge>
                  </div>
                  
                  <h3 className="text-2xl font-bold text-charcoal">{currentStep.title}</h3>
                </div>
                
                {/* Step Content */}
                <div className="prose prose-lg max-w-none">
                  <div className="bg-gradient-to-r from-emerald/5 to-bright-green/5 rounded-2xl p-8">
                    <p className="text-grey leading-relaxed whitespace-pre-wrap">
                      {currentStep.content}
                    </p>
                  </div>
                </div>
                
                {/* Interactive Elements */}
                {currentStep.interactiveElements && currentStep.interactiveElements.length > 0 && (
                  <div className="space-y-6">
                    <h4 className="text-lg font-semibold text-charcoal flex items-center gap-2">
                      <HelpCircle className="h-5 w-5 text-bright-green" />
                      Interactive Elements
                    </h4>
                    
                    {currentStep.interactiveElements.map((element, index) => (
                      <div key={index} className="bg-bright-green/5 rounded-2xl p-6 border border-bright-green/20">
                        <div className="space-y-4">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="border-bright-green/30 text-bright-green capitalize">
                              {element.type}
                            </Badge>
                          </div>
                          
                          <p className="text-charcoal">{element.content}</p>
                          
                          {element.answer && (
                            <div className="space-y-2">
                              <input
                                type="text"
                                value={interactiveAnswers[`${currentStep.id}-${index}`] || ""}
                                onChange={(e) => setInteractiveAnswers(prev => ({
                                  ...prev,
                                  [`${currentStep.id}-${index}`]: e.target.value
                                }))}
                                placeholder="Your answer..."
                                className="w-full px-4 py-2 rounded-lg border border-grey/20 bg-white focus:outline-none focus:border-bright-green/40"
                              />
                              
                              {interactiveAnswers[`${currentStep.id}-${index}`] && (
                                <div className="text-sm text-grey">
                                  <strong>Expected:</strong> {element.answer}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          onClick={previousStep}
          disabled={currentStepIndex === 0}
          variant="outline"
          className="border-emerald/30 text-emerald hover:bg-emerald/10 disabled:opacity-50"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous Step
        </Button>
        
        <div className="text-center">
          <p className="text-sm text-grey">
            Step {currentStepIndex + 1} of {currentModule.steps.length}
          </p>
        </div>
        
        <Button
          onClick={currentStepIndex === currentModule.steps.length - 1 ? completeStep : nextStep}
          className="bg-emerald hover:bg-emerald-deep text-white"
        >
          {currentStepIndex === currentModule.steps.length - 1 ? (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Complete Module
            </>
          ) : (
            <>
              Next Step
              <ChevronRight className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
