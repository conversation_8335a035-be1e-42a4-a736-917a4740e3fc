"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/Card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Layers, 
  Target, 
  Brain,
  Clock,
  CheckCircle,
  XCircle,
  RotateCcw,
  ChevronRight,
  ChevronLeft,
  Award,
  TrendingUp,
  BookOpen,
  Shuffle,
  Lightbulb,
  Link
} from "lucide-react";
import { toast } from "sonner";

interface MatchItem {
  id: string;
  content: string;
  category: string;
}

interface CorrectMatch {
  leftId: string;
  rightId: string;
  explanation: string;
}

interface MatchExercise {
  id: string;
  title: string;
  leftItems: MatchItem[];
  rightItems: MatchItem[];
  correctMatches: CorrectMatch[];
  difficulty: "easy" | "medium" | "hard";
}

interface MatchConceptsProps {
  exercises: MatchExercise[];
  planId: string;
  sessionId: string;
  onProgress?: (progress: any) => void;
  onComplete?: () => void;
}

interface UserMatch {
  leftId: string;
  rightId: string;
}

interface ExerciseResult {
  exerciseId: string;
  userMatches: UserMatch[];
  correctMatches: number;
  totalMatches: number;
  timeSpent: number;
}

export default function MatchConcepts({ 
  exercises, 
  planId, 
  sessionId, 
  onProgress, 
  onComplete 
}: MatchConceptsProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [userMatches, setUserMatches] = useState<UserMatch[]>([]);
  const [selectedLeft, setSelectedLeft] = useState<string | null>(null);
  const [selectedRight, setSelectedRight] = useState<string | null>(null);
  const [showResults, setShowResults] = useState(false);
  const [results, setResults] = useState<ExerciseResult[]>([]);
  const [sessionStartTime] = useState(Date.now());
  const [exerciseStartTime, setExerciseStartTime] = useState(Date.now());
  const [isComplete, setIsComplete] = useState(false);
  const [shuffledRight, setShuffledRight] = useState<MatchItem[]>([]);

  const currentExercise = exercises[currentIndex];
  const progress = ((currentIndex + (showResults ? 1 : 0)) / exercises.length) * 100;
  const totalCorrect = results.reduce((sum, r) => sum + r.correctMatches, 0);
  const totalPossible = results.reduce((sum, r) => sum + r.totalMatches, 0);
  const accuracy = totalPossible > 0 ? (totalCorrect / totalPossible) * 100 : 0;

  // Shuffle right items when exercise changes
  useEffect(() => {
    if (currentExercise) {
      const shuffled = [...currentExercise.rightItems].sort(() => Math.random() - 0.5);
      setShuffledRight(shuffled);
    }
  }, [currentExercise]);

  // Handle item selection
  const handleLeftSelect = useCallback((itemId: string) => {
    if (showResults) return;
    setSelectedLeft(selectedLeft === itemId ? null : itemId);
    setSelectedRight(null);
  }, [selectedLeft, showResults]);

  const handleRightSelect = useCallback((itemId: string) => {
    if (showResults) return;
    
    if (selectedLeft) {
      // Create a match
      const newMatch: UserMatch = { leftId: selectedLeft, rightId: itemId };
      setUserMatches(prev => {
        // Remove any existing matches for these items
        const filtered = prev.filter(m => m.leftId !== selectedLeft && m.rightId !== itemId);
        return [...filtered, newMatch];
      });
      setSelectedLeft(null);
      setSelectedRight(null);
    } else {
      setSelectedRight(selectedRight === itemId ? null : itemId);
    }
  }, [selectedLeft, selectedRight, showResults]);

  // Check answers
  const checkAnswers = useCallback(() => {
    if (userMatches.length !== currentExercise.correctMatches.length) {
      toast.error("Please match all items before checking answers");
      return;
    }

    const timeSpent = Date.now() - exerciseStartTime;
    let correctCount = 0;

    userMatches.forEach(userMatch => {
      const isCorrect = currentExercise.correctMatches.some(
        correct => correct.leftId === userMatch.leftId && correct.rightId === userMatch.rightId
      );
      if (isCorrect) correctCount++;
    });

    const result: ExerciseResult = {
      exerciseId: currentExercise.id,
      userMatches,
      correctMatches: correctCount,
      totalMatches: currentExercise.correctMatches.length,
      timeSpent,
    };

    setResults(prev => [...prev, result]);
    setShowResults(true);

    const percentage = Math.round((correctCount / currentExercise.correctMatches.length) * 100);
    if (percentage === 100) {
      toast.success("Perfect! All matches correct!");
    } else if (percentage >= 70) {
      toast.success(`Good job! ${percentage}% correct`);
    } else {
      toast.error(`${percentage}% correct. Review the explanations.`);
    }
  }, [userMatches, currentExercise, exerciseStartTime]);

  // Move to next exercise
  const nextExercise = useCallback(() => {
    if (currentIndex < exercises.length - 1) {
      setCurrentIndex(prev => prev + 1);
      setUserMatches([]);
      setSelectedLeft(null);
      setSelectedRight(null);
      setShowResults(false);
      setExerciseStartTime(Date.now());
    } else {
      // Complete session
      setIsComplete(true);
      const totalTime = Math.round((Date.now() - sessionStartTime) / 1000 / 60);
      const finalAccuracy = Math.round(accuracy);
      
      onProgress?.({
        completed: true,
        score: finalAccuracy,
        timeSpent: totalTime,
        attempts: 1,
        details: {
          totalExercises: exercises.length,
          totalCorrect,
          totalPossible,
          accuracy: finalAccuracy,
        }
      });
      
      onComplete?.();
    }
  }, [currentIndex, exercises.length, sessionStartTime, accuracy, totalCorrect, totalPossible, onProgress, onComplete]);

  // Previous exercise
  const previousExercise = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
      setUserMatches([]);
      setSelectedLeft(null);
      setSelectedRight(null);
      setShowResults(false);
      setExerciseStartTime(Date.now());
    }
  }, [currentIndex]);

  // Restart session
  const restartSession = useCallback(() => {
    setCurrentIndex(0);
    setUserMatches([]);
    setSelectedLeft(null);
    setSelectedRight(null);
    setShowResults(false);
    setResults([]);
    setIsComplete(false);
    setExerciseStartTime(Date.now());
  }, []);

  // Clear all matches
  const clearMatches = useCallback(() => {
    setUserMatches([]);
    setSelectedLeft(null);
    setSelectedRight(null);
  }, []);

  if (!currentExercise) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <BookOpen className="h-12 w-12 text-grey mx-auto mb-4" />
          <p className="text-grey">No matching exercises available</p>
        </div>
      </div>
    );
  }

  const difficultyColors = {
    easy: "bg-lime-green/20 text-lime-green border-lime-green/30",
    medium: "bg-bright-green/20 text-bright-green border-bright-green/30",
    hard: "bg-emerald/20 text-emerald border-emerald/30"
  };

  // Check if item is matched
  const isMatched = (itemId: string, side: 'left' | 'right') => {
    return userMatches.some(match => 
      side === 'left' ? match.leftId === itemId : match.rightId === itemId
    );
  };

  // Get match for item
  const getMatchForItem = (itemId: string, side: 'left' | 'right') => {
    return userMatches.find(match => 
      side === 'left' ? match.leftId === itemId : match.rightId === itemId
    );
  };

  // Check if match is correct
  const isMatchCorrect = (userMatch: UserMatch) => {
    return currentExercise.correctMatches.some(
      correct => correct.leftId === userMatch.leftId && correct.rightId === userMatch.rightId
    );
  };

  if (isComplete) {
    return (
      <div className="w-full max-w-4xl mx-auto p-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center space-y-8"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald/20 via-bright-green/20 to-lime-green/20 rounded-3xl blur-xl" />
            <div className="relative bg-white/90 backdrop-blur-lg rounded-3xl border border-white/20 p-12 shadow-2xl">
              <Award className="h-16 w-16 text-emerald mx-auto mb-6" />
              <h2 className="text-4xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent mb-4">
                Matching Complete!
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                <div className="bg-emerald/10 rounded-2xl p-6">
                  <Target className="h-8 w-8 text-emerald mx-auto mb-2" />
                  <div className="text-3xl font-bold text-emerald">{Math.round(accuracy)}%</div>
                  <div className="text-sm text-grey">Accuracy</div>
                </div>
                
                <div className="bg-bright-green/10 rounded-2xl p-6">
                  <TrendingUp className="h-8 w-8 text-bright-green mx-auto mb-2" />
                  <div className="text-3xl font-bold text-bright-green">{totalCorrect}</div>
                  <div className="text-sm text-grey">Correct Matches</div>
                </div>
                
                <div className="bg-lime-green/10 rounded-2xl p-6">
                  <Clock className="h-8 w-8 text-lime-green mx-auto mb-2" />
                  <div className="text-3xl font-bold text-lime-green">
                    {Math.round((Date.now() - sessionStartTime) / 1000 / 60)}m
                  </div>
                  <div className="text-sm text-grey">Time Spent</div>
                </div>
              </div>
              
              <div className="flex justify-center gap-4 mt-8">
                <Button
                  onClick={restartSession}
                  className="bg-emerald hover:bg-emerald-deep text-white"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Practice Again
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto p-6 space-y-8">
      {/* Header with Stats */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald/10 via-bright-green/10 to-lime-green/10 rounded-3xl blur-xl" />
        <div className="relative bg-white/80 backdrop-blur-lg rounded-3xl border border-white/20 p-8 shadow-2xl">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent">
                Match Concepts
              </h2>
              <p className="text-grey text-lg">Connect related ideas and concepts</p>
            </div>
            
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center gap-2 px-4 py-2 bg-emerald/10 rounded-full">
                <Target className="h-4 w-4 text-emerald" />
                <span className="text-sm font-medium text-emerald">
                  {Math.round(accuracy)}% Accuracy
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-bright-green/10 rounded-full">
                <Link className="h-4 w-4 text-bright-green" />
                <span className="text-sm font-medium text-bright-green">
                  {userMatches.length}/{currentExercise.correctMatches.length} Matched
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-lime-green/10 rounded-full">
                <Brain className="h-4 w-4 text-lime-green" />
                <span className="text-sm font-medium text-lime-green">
                  {currentIndex + 1}/{exercises.length}
                </span>
              </div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-6 space-y-2">
            <div className="flex justify-between text-sm text-grey">
              <span>Exercise Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress 
              value={progress} 
              className="h-3 bg-grey/20"
            />
          </div>
        </div>
      </div>

      {/* Exercise Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-4">
          <Badge className={cn("px-4 py-2", difficultyColors[currentExercise.difficulty])}>
            {currentExercise.difficulty.toUpperCase()}
          </Badge>
          <Badge variant="outline" className="px-4 py-2 border-grey/30 text-grey">
            Exercise {currentIndex + 1}
          </Badge>
        </div>
        
        <h3 className="text-2xl font-bold text-charcoal">{currentExercise.title}</h3>
        <p className="text-grey">Click items from both columns to create matches</p>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-center gap-4">
        <Button
          onClick={clearMatches}
          variant="outline"
          size="sm"
          className="border-red-300 text-red-600 hover:bg-red-50"
          disabled={showResults || userMatches.length === 0}
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          Clear All
        </Button>
        
        <Button
          onClick={checkAnswers}
          disabled={showResults || userMatches.length !== currentExercise.correctMatches.length}
          className="bg-emerald hover:bg-emerald-deep text-white"
        >
          <CheckCircle className="h-4 w-4 mr-2" />
          Check Answers
        </Button>
      </div>

      {/* Matching Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-charcoal text-center">Match these...</h4>
          <div className="space-y-3">
            {currentExercise.leftItems.map((item, index) => {
              const isSelected = selectedLeft === item.id;
              const matched = isMatched(item.id, 'left');
              const userMatch = getMatchForItem(item.id, 'left');
              const isCorrect = userMatch && isMatchCorrect(userMatch);
              
              return (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card 
                    className={cn(
                      "cursor-pointer transition-all duration-300",
                      "border-2 bg-white/90 backdrop-blur-sm",
                      !showResults && !matched && "hover:border-emerald/40 hover:bg-emerald/5",
                      isSelected && "border-emerald bg-emerald/10",
                      matched && !showResults && "border-bright-green bg-bright-green/10",
                      showResults && matched && isCorrect && "border-emerald bg-emerald/10",
                      showResults && matched && !isCorrect && "border-red-400 bg-red-50"
                    )}
                    onClick={() => handleLeftSelect(item.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <p className="text-charcoal font-medium">{item.content}</p>
                        {matched && (
                          <div className="flex items-center gap-2">
                            {showResults && (
                              isCorrect ? (
                                <CheckCircle className="h-5 w-5 text-emerald" />
                              ) : (
                                <XCircle className="h-5 w-5 text-red-500" />
                              )
                            )}
                            <Link className="h-4 w-4 text-bright-green" />
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-charcoal text-center">...with these</h4>
          <div className="space-y-3">
            {shuffledRight.map((item, index) => {
              const isSelected = selectedRight === item.id;
              const matched = isMatched(item.id, 'right');
              const userMatch = userMatches.find(m => m.rightId === item.id);
              const isCorrect = userMatch && isMatchCorrect(userMatch);
              
              return (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card 
                    className={cn(
                      "cursor-pointer transition-all duration-300",
                      "border-2 bg-white/90 backdrop-blur-sm",
                      !showResults && !matched && "hover:border-emerald/40 hover:bg-emerald/5",
                      isSelected && "border-emerald bg-emerald/10",
                      matched && !showResults && "border-bright-green bg-bright-green/10",
                      showResults && matched && isCorrect && "border-emerald bg-emerald/10",
                      showResults && matched && !isCorrect && "border-red-400 bg-red-50"
                    )}
                    onClick={() => handleRightSelect(item.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <p className="text-charcoal font-medium">{item.content}</p>
                        {matched && (
                          <div className="flex items-center gap-2">
                            <Link className="h-4 w-4 text-bright-green" />
                            {showResults && (
                              isCorrect ? (
                                <CheckCircle className="h-5 w-5 text-emerald" />
                              ) : (
                                <XCircle className="h-5 w-5 text-red-500" />
                              )
                            )}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Explanations */}
      <AnimatePresence>
        {showResults && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-gradient-to-r from-bright-green/10 to-lime-green/10 rounded-2xl p-8 border border-bright-green/20"
          >
            <div className="flex items-start gap-4 mb-6">
              <Lightbulb className="h-6 w-6 text-bright-green mt-1 flex-shrink-0" />
              <h4 className="text-xl font-semibold text-charcoal">Explanations:</h4>
            </div>
            
            <div className="space-y-4">
              {currentExercise.correctMatches.map((match, index) => {
                const leftItem = currentExercise.leftItems.find(item => item.id === match.leftId);
                const rightItem = currentExercise.rightItems.find(item => item.id === match.rightId);
                const userMatch = userMatches.find(um => um.leftId === match.leftId);
                const isCorrect = userMatch?.rightId === match.rightId;
                
                return (
                  <div key={index} className="flex items-start gap-4">
                    <div className={cn(
                      "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                      isCorrect ? "bg-emerald text-white" : "bg-red-500 text-white"
                    )}>
                      {isCorrect ? <CheckCircle className="h-4 w-4" /> : <XCircle className="h-4 w-4" />}
                    </div>
                    <div className="space-y-2">
                      <p className="font-medium text-charcoal">
                        <span className="text-emerald">{leftItem?.content}</span> → <span className="text-bright-green">{rightItem?.content}</span>
                      </p>
                      <p className="text-grey text-sm">{match.explanation}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          onClick={previousExercise}
          disabled={currentIndex === 0}
          variant="outline"
          className="border-emerald/30 text-emerald hover:bg-emerald/10 disabled:opacity-50"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <div className="text-center">
          <p className="text-sm text-grey">
            {selectedLeft ? "Now select an item from the right column" : "Select an item from the left column first"}
          </p>
        </div>
        
        {showResults && (
          <Button
            onClick={nextExercise}
            className="bg-emerald hover:bg-emerald-deep text-white"
          >
            {currentIndex === exercises.length - 1 ? "Complete" : "Next Exercise"}
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        )}
        
        {!showResults && (
          <div className="w-24" /> // Spacer to center the instruction
        )}
      </div>
    </div>
  );
}
