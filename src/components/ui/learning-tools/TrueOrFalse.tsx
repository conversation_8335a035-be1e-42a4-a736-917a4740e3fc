"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/Card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Target, 
  Brain,
  Lightbulb,
  RotateCcw,
  ChevronRight,
  ChevronLeft,
  AlertCircle,
  BookOpen,
  TrendingUp,
  Award
} from "lucide-react";
import { toast } from "sonner";

interface TrueOrFalseQuestion {
  id: string;
  statement: string;
  correct: boolean;
  explanation: string;
  difficulty: "easy" | "medium" | "hard";
  category: string;
}

interface TrueOrFalseProps {
  questions: TrueOrFalseQuestion[];
  planId: string;
  sessionId: string;
  onProgress?: (progress: any) => void;
  onComplete?: () => void;
}

interface QuestionResult {
  questionId: string;
  userAnswer: boolean;
  correct: boolean;
  isCorrect: boolean;
  timeSpent: number;
}

export default function TrueOrFalse({ 
  questions, 
  planId, 
  sessionId, 
  onProgress, 
  onComplete 
}: TrueOrFalseProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [userAnswer, setUserAnswer] = useState<boolean | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);
  const [results, setResults] = useState<QuestionResult[]>([]);
  const [sessionStartTime] = useState(Date.now());
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [isComplete, setIsComplete] = useState(false);
  const [streak, setStreak] = useState(0);
  const [bestStreak, setBestStreak] = useState(0);

  const currentQuestion = questions[currentIndex];
  const progress = ((currentIndex + (showExplanation ? 1 : 0)) / questions.length) * 100;
  const correctAnswers = results.filter(r => r.isCorrect).length;
  const accuracy = results.length > 0 ? (correctAnswers / results.length) * 100 : 0;

  // Handle answer selection
  const handleAnswer = useCallback((answer: boolean) => {
    if (userAnswer !== null) return;
    
    const timeSpent = Date.now() - questionStartTime;
    const isCorrect = answer === currentQuestion.correct;
    
    setUserAnswer(answer);
    setShowExplanation(true);
    
    const result: QuestionResult = {
      questionId: currentQuestion.id,
      userAnswer: answer,
      correct: currentQuestion.correct,
      isCorrect,
      timeSpent,
    };
    
    setResults(prev => [...prev, result]);
    
    // Update streak
    if (isCorrect) {
      setStreak(prev => {
        const newStreak = prev + 1;
        setBestStreak(current => Math.max(current, newStreak));
        return newStreak;
      });
    } else {
      setStreak(0);
    }
    
    // Show feedback
    if (isCorrect) {
      toast.success("Correct! Well done!");
    } else {
      toast.error("Incorrect. Check the explanation.");
    }
  }, [userAnswer, questionStartTime, currentQuestion]);

  // Move to next question
  const nextQuestion = useCallback(() => {
    if (currentIndex < questions.length - 1) {
      setCurrentIndex(prev => prev + 1);
      setUserAnswer(null);
      setShowExplanation(false);
      setQuestionStartTime(Date.now());
    } else {
      // Complete session
      setIsComplete(true);
      const totalTime = Math.round((Date.now() - sessionStartTime) / 1000 / 60);
      const finalAccuracy = Math.round(accuracy);
      
      onProgress?.({
        completed: true,
        score: finalAccuracy,
        timeSpent: totalTime,
        attempts: 1,
        details: {
          totalQuestions: questions.length,
          correctAnswers,
          accuracy: finalAccuracy,
          bestStreak,
          averageTimePerQuestion: Math.round(results.reduce((sum, r) => sum + r.timeSpent, 0) / results.length / 1000),
        }
      });
      
      onComplete?.();
    }
  }, [currentIndex, questions.length, sessionStartTime, accuracy, correctAnswers, bestStreak, results, onProgress, onComplete]);

  // Previous question (for review)
  const previousQuestion = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
      setUserAnswer(null);
      setShowExplanation(false);
      setQuestionStartTime(Date.now());
    }
  }, [currentIndex]);

  // Restart session
  const restartSession = useCallback(() => {
    setCurrentIndex(0);
    setUserAnswer(null);
    setShowExplanation(false);
    setResults([]);
    setIsComplete(false);
    setStreak(0);
    setBestStreak(0);
    setQuestionStartTime(Date.now());
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (userAnswer !== null) return;
      
      switch (e.key) {
        case "t":
        case "T":
        case "1":
          e.preventDefault();
          handleAnswer(true);
          break;
        case "f":
        case "F":
        case "0":
          e.preventDefault();
          handleAnswer(false);
          break;
        case "Enter":
        case " ":
          if (showExplanation) {
            e.preventDefault();
            nextQuestion();
          }
          break;
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [userAnswer, showExplanation, handleAnswer, nextQuestion]);

  if (!currentQuestion) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <BookOpen className="h-12 w-12 text-grey mx-auto mb-4" />
          <p className="text-grey">No questions available</p>
        </div>
      </div>
    );
  }

  const difficultyColors = {
    easy: "bg-lime-green/20 text-lime-green border-lime-green/30",
    medium: "bg-bright-green/20 text-bright-green border-bright-green/30",
    hard: "bg-emerald/20 text-emerald border-emerald/30"
  };

  if (isComplete) {
    return (
      <div className="w-full max-w-4xl mx-auto p-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center space-y-8"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald/20 via-bright-green/20 to-lime-green/20 rounded-3xl blur-xl" />
            <div className="relative bg-white/90 backdrop-blur-lg rounded-3xl border border-white/20 p-12 shadow-2xl">
              <Award className="h-16 w-16 text-emerald mx-auto mb-6" />
              <h2 className="text-4xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent mb-4">
                Session Complete!
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                <div className="bg-emerald/10 rounded-2xl p-6">
                  <Target className="h-8 w-8 text-emerald mx-auto mb-2" />
                  <div className="text-3xl font-bold text-emerald">{Math.round(accuracy)}%</div>
                  <div className="text-sm text-grey">Accuracy</div>
                </div>
                
                <div className="bg-bright-green/10 rounded-2xl p-6">
                  <TrendingUp className="h-8 w-8 text-bright-green mx-auto mb-2" />
                  <div className="text-3xl font-bold text-bright-green">{bestStreak}</div>
                  <div className="text-sm text-grey">Best Streak</div>
                </div>
                
                <div className="bg-lime-green/10 rounded-2xl p-6">
                  <Clock className="h-8 w-8 text-lime-green mx-auto mb-2" />
                  <div className="text-3xl font-bold text-lime-green">
                    {Math.round((Date.now() - sessionStartTime) / 1000 / 60)}m
                  </div>
                  <div className="text-sm text-grey">Time Spent</div>
                </div>
              </div>
              
              <div className="flex justify-center gap-4 mt-8">
                <Button
                  onClick={restartSession}
                  className="bg-emerald hover:bg-emerald-deep text-white"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-8">
      {/* Header with Stats */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald/10 via-bright-green/10 to-lime-green/10 rounded-3xl blur-xl" />
        <div className="relative bg-white/80 backdrop-blur-lg rounded-3xl border border-white/20 p-8 shadow-2xl">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent">
                True or False Challenge
              </h2>
              <p className="text-grey text-lg">Test your knowledge with precision</p>
            </div>
            
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center gap-2 px-4 py-2 bg-emerald/10 rounded-full">
                <Target className="h-4 w-4 text-emerald" />
                <span className="text-sm font-medium text-emerald">
                  {Math.round(accuracy)}% Accuracy
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-bright-green/10 rounded-full">
                <TrendingUp className="h-4 w-4 text-bright-green" />
                <span className="text-sm font-medium text-bright-green">
                  Streak: {streak}
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-lime-green/10 rounded-full">
                <Brain className="h-4 w-4 text-lime-green" />
                <span className="text-sm font-medium text-lime-green">
                  {currentIndex + 1}/{questions.length}
                </span>
              </div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-6 space-y-2">
            <div className="flex justify-between text-sm text-grey">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress 
              value={progress} 
              className="h-3 bg-grey/20"
            />
          </div>
        </div>
      </div>

      {/* Question Card */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-br from-white/50 to-emerald/5 rounded-3xl blur-sm" />
        <Card className="relative bg-white/90 backdrop-blur-lg border-2 border-emerald/20 shadow-2xl rounded-3xl overflow-hidden">
          <CardContent className="p-12">
            <div className="text-center space-y-8">
              {/* Question Header */}
              <div className="flex items-center justify-center gap-4 mb-8">
                <Badge className={cn("px-4 py-2", difficultyColors[currentQuestion.difficulty])}>
                  {currentQuestion.difficulty.toUpperCase()}
                </Badge>
                <Badge variant="outline" className="px-4 py-2 border-grey/30 text-grey">
                  {currentQuestion.category}
                </Badge>
              </div>
              
              {/* Question Statement */}
              <div className="space-y-6">
                <div className="p-6 bg-gradient-to-r from-emerald/5 to-bright-green/5 rounded-2xl">
                  <h3 className="text-2xl font-bold text-charcoal leading-relaxed">
                    {currentQuestion.statement}
                  </h3>
                </div>
                
                {/* Answer Buttons */}
                {userAnswer === null && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex items-center justify-center gap-8"
                  >
                    <Button
                      onClick={() => handleAnswer(true)}
                      size="lg"
                      className="bg-emerald hover:bg-emerald-deep text-white px-12 py-6 text-xl rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <CheckCircle className="h-6 w-6 mr-3" />
                      TRUE
                    </Button>
                    
                    <Button
                      onClick={() => handleAnswer(false)}
                      size="lg"
                      variant="outline"
                      className="border-2 border-red-300 text-red-600 hover:bg-red-50 px-12 py-6 text-xl rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <XCircle className="h-6 w-6 mr-3" />
                      FALSE
                    </Button>
                  </motion.div>
                )}
                
                {/* Answer Feedback */}
                {userAnswer !== null && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="space-y-6"
                  >
                    <div className={cn(
                      "p-6 rounded-2xl border-2",
                      userAnswer === currentQuestion.correct
                        ? "bg-emerald/10 border-emerald/30 text-emerald"
                        : "bg-red-50 border-red-300 text-red-600"
                    )}>
                      <div className="flex items-center justify-center gap-3 mb-4">
                        {userAnswer === currentQuestion.correct ? (
                          <CheckCircle className="h-8 w-8" />
                        ) : (
                          <XCircle className="h-8 w-8" />
                        )}
                        <span className="text-xl font-bold">
                          {userAnswer === currentQuestion.correct ? "Correct!" : "Incorrect"}
                        </span>
                      </div>
                      
                      <div className="text-center">
                        <p className="text-lg mb-2">
                          The correct answer is: <strong>{currentQuestion.correct ? "TRUE" : "FALSE"}</strong>
                        </p>
                        <p className="text-base opacity-90">
                          You answered: <strong>{userAnswer ? "TRUE" : "FALSE"}</strong>
                        </p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Explanation */}
      <AnimatePresence>
        {showExplanation && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-gradient-to-r from-bright-green/10 to-lime-green/10 rounded-2xl p-8 border border-bright-green/20"
          >
            <div className="flex items-start gap-4">
              <Lightbulb className="h-6 w-6 text-bright-green mt-1 flex-shrink-0" />
              <div className="space-y-3">
                <h4 className="text-xl font-semibold text-charcoal">Explanation:</h4>
                <p className="text-grey text-lg leading-relaxed">
                  {currentQuestion.explanation}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          onClick={previousQuestion}
          disabled={currentIndex === 0}
          variant="outline"
          className="border-emerald/30 text-emerald hover:bg-emerald/10 disabled:opacity-50"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <div className="text-center">
          <p className="text-sm text-grey">
            Press <kbd className="px-2 py-1 bg-grey/10 rounded text-xs">T</kbd> for True, 
            <kbd className="px-2 py-1 bg-grey/10 rounded text-xs ml-1">F</kbd> for False
          </p>
        </div>
        
        {showExplanation && (
          <Button
            onClick={nextQuestion}
            className="bg-emerald hover:bg-emerald-deep text-white"
          >
            {currentIndex === questions.length - 1 ? "Complete" : "Next"}
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        )}
        
        {!showExplanation && (
          <div className="w-24" /> // Spacer to center the keyboard hint
        )}
      </div>
    </div>
  );
}
