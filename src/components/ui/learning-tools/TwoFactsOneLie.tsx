"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/Card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Eye, 
  EyeOff, 
  Target, 
  Brain,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Lightbulb,
  ChevronRight,
  ChevronLeft,
  RotateCcw,
  Award,
  TrendingUp,
  Search,
  BookOpen
} from "lucide-react";
import { toast } from "sonner";

interface TwoFactsOneLieRound {
  id: string;
  topic: string;
  statements: [string, string, string];
  lieIndex: number; // 0, 1, or 2
  explanations: [string, string, string];
  difficulty: "easy" | "medium" | "hard";
}

interface TwoFactsOneLieProps {
  rounds: TwoFactsOneLieRound[];
  planId: string;
  sessionId: string;
  onProgress?: (progress: any) => void;
  onComplete?: () => void;
}

interface RoundResult {
  roundId: string;
  userChoice: number;
  correctLieIndex: number;
  isCorrect: boolean;
  timeSpent: number;
}

export default function TwoFactsOneLie({ 
  rounds, 
  planId, 
  sessionId, 
  onProgress, 
  onComplete 
}: TwoFactsOneLieProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedStatement, setSelectedStatement] = useState<number | null>(null);
  const [showExplanations, setShowExplanations] = useState(false);
  const [results, setResults] = useState<RoundResult[]>([]);
  const [sessionStartTime] = useState(Date.now());
  const [roundStartTime, setRoundStartTime] = useState(Date.now());
  const [isComplete, setIsComplete] = useState(false);
  const [streak, setStreak] = useState(0);
  const [bestStreak, setBestStreak] = useState(0);

  const currentRound = rounds[currentIndex];
  const progress = ((currentIndex + (showExplanations ? 1 : 0)) / rounds.length) * 100;
  const correctAnswers = results.filter(r => r.isCorrect).length;
  const accuracy = results.length > 0 ? (correctAnswers / results.length) * 100 : 0;

  // Handle statement selection
  const handleStatementSelect = useCallback((statementIndex: number) => {
    if (selectedStatement !== null) return;
    
    const timeSpent = Date.now() - roundStartTime;
    const isCorrect = statementIndex === currentRound.lieIndex;
    
    setSelectedStatement(statementIndex);
    setShowExplanations(true);
    
    const result: RoundResult = {
      roundId: currentRound.id,
      userChoice: statementIndex,
      correctLieIndex: currentRound.lieIndex,
      isCorrect,
      timeSpent,
    };
    
    setResults(prev => [...prev, result]);
    
    // Update streak
    if (isCorrect) {
      setStreak(prev => {
        const newStreak = prev + 1;
        setBestStreak(current => Math.max(current, newStreak));
        return newStreak;
      });
      toast.success("Excellent! You found the lie!");
    } else {
      setStreak(0);
      toast.error("Not quite right. Check the explanations!");
    }
  }, [selectedStatement, roundStartTime, currentRound]);

  // Move to next round
  const nextRound = useCallback(() => {
    if (currentIndex < rounds.length - 1) {
      setCurrentIndex(prev => prev + 1);
      setSelectedStatement(null);
      setShowExplanations(false);
      setRoundStartTime(Date.now());
    } else {
      // Complete session
      setIsComplete(true);
      const totalTime = Math.round((Date.now() - sessionStartTime) / 1000 / 60);
      const finalAccuracy = Math.round(accuracy);
      
      onProgress?.({
        completed: true,
        score: finalAccuracy,
        timeSpent: totalTime,
        attempts: 1,
        details: {
          totalRounds: rounds.length,
          correctAnswers,
          accuracy: finalAccuracy,
          bestStreak,
          averageTimePerRound: Math.round(results.reduce((sum, r) => sum + r.timeSpent, 0) / results.length / 1000),
        }
      });
      
      onComplete?.();
    }
  }, [currentIndex, rounds.length, sessionStartTime, accuracy, correctAnswers, bestStreak, results, onProgress, onComplete]);

  // Previous round (for review)
  const previousRound = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
      setSelectedStatement(null);
      setShowExplanations(false);
      setRoundStartTime(Date.now());
    }
  }, [currentIndex]);

  // Restart session
  const restartSession = useCallback(() => {
    setCurrentIndex(0);
    setSelectedStatement(null);
    setShowExplanations(false);
    setResults([]);
    setIsComplete(false);
    setStreak(0);
    setBestStreak(0);
    setRoundStartTime(Date.now());
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (selectedStatement !== null) return;
      
      switch (e.key) {
        case "1":
          e.preventDefault();
          handleStatementSelect(0);
          break;
        case "2":
          e.preventDefault();
          handleStatementSelect(1);
          break;
        case "3":
          e.preventDefault();
          handleStatementSelect(2);
          break;
        case "Enter":
        case " ":
          if (showExplanations) {
            e.preventDefault();
            nextRound();
          }
          break;
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [selectedStatement, showExplanations, handleStatementSelect, nextRound]);

  if (!currentRound) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <BookOpen className="h-12 w-12 text-grey mx-auto mb-4" />
          <p className="text-grey">No rounds available</p>
        </div>
      </div>
    );
  }

  const difficultyColors = {
    easy: "bg-lime-green/20 text-lime-green border-lime-green/30",
    medium: "bg-bright-green/20 text-bright-green border-bright-green/30",
    hard: "bg-emerald/20 text-emerald border-emerald/30"
  };

  if (isComplete) {
    return (
      <div className="w-full max-w-4xl mx-auto p-6">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center space-y-8"
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-emerald/20 via-bright-green/20 to-lime-green/20 rounded-3xl blur-xl" />
            <div className="relative bg-white/90 backdrop-blur-lg rounded-3xl border border-white/20 p-12 shadow-2xl">
              <Award className="h-16 w-16 text-emerald mx-auto mb-6" />
              <h2 className="text-4xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent mb-4">
                Detective Work Complete!
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                <div className="bg-emerald/10 rounded-2xl p-6">
                  <Target className="h-8 w-8 text-emerald mx-auto mb-2" />
                  <div className="text-3xl font-bold text-emerald">{Math.round(accuracy)}%</div>
                  <div className="text-sm text-grey">Detection Rate</div>
                </div>
                
                <div className="bg-bright-green/10 rounded-2xl p-6">
                  <TrendingUp className="h-8 w-8 text-bright-green mx-auto mb-2" />
                  <div className="text-3xl font-bold text-bright-green">{bestStreak}</div>
                  <div className="text-sm text-grey">Best Streak</div>
                </div>
                
                <div className="bg-lime-green/10 rounded-2xl p-6">
                  <Clock className="h-8 w-8 text-lime-green mx-auto mb-2" />
                  <div className="text-3xl font-bold text-lime-green">
                    {Math.round((Date.now() - sessionStartTime) / 1000 / 60)}m
                  </div>
                  <div className="text-sm text-grey">Investigation Time</div>
                </div>
              </div>
              
              <div className="flex justify-center gap-4 mt-8">
                <Button
                  onClick={restartSession}
                  className="bg-emerald hover:bg-emerald-deep text-white"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Investigate Again
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-8">
      {/* Header with Stats */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald/10 via-bright-green/10 to-lime-green/10 rounded-3xl blur-xl" />
        <div className="relative bg-white/80 backdrop-blur-lg rounded-3xl border border-white/20 p-8 shadow-2xl">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent">
                Two Facts, One Lie
              </h2>
              <p className="text-grey text-lg">Sharpen your critical thinking skills</p>
            </div>
            
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center gap-2 px-4 py-2 bg-emerald/10 rounded-full">
                <Target className="h-4 w-4 text-emerald" />
                <span className="text-sm font-medium text-emerald">
                  {Math.round(accuracy)}% Detection Rate
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-bright-green/10 rounded-full">
                <TrendingUp className="h-4 w-4 text-bright-green" />
                <span className="text-sm font-medium text-bright-green">
                  Streak: {streak}
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-lime-green/10 rounded-full">
                <Brain className="h-4 w-4 text-lime-green" />
                <span className="text-sm font-medium text-lime-green">
                  {currentIndex + 1}/{rounds.length}
                </span>
              </div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-6 space-y-2">
            <div className="flex justify-between text-sm text-grey">
              <span>Investigation Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress 
              value={progress} 
              className="h-3 bg-grey/20"
            />
          </div>
        </div>
      </div>

      {/* Round Info */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-4">
          <Badge className={cn("px-4 py-2", difficultyColors[currentRound.difficulty])}>
            {currentRound.difficulty.toUpperCase()}
          </Badge>
          <Badge variant="outline" className="px-4 py-2 border-grey/30 text-grey">
            Topic: {currentRound.topic}
          </Badge>
        </div>
        
        <div className="bg-gradient-to-r from-emerald/5 to-bright-green/5 rounded-2xl p-6">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Search className="h-6 w-6 text-emerald" />
            <h3 className="text-2xl font-bold text-charcoal">Find the Lie</h3>
          </div>
          <p className="text-grey text-lg">
            Two of these statements are facts, one is a lie. Can you spot the deception?
          </p>
        </div>
      </div>

      {/* Statements */}
      <div className="grid gap-6">
        {currentRound.statements.map((statement, index) => {
          const isSelected = selectedStatement === index;
          const isLie = index === currentRound.lieIndex;
          const showResult = showExplanations;
          
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card 
                className={cn(
                  "cursor-pointer transition-all duration-300 hover:shadow-lg",
                  "border-2 bg-white/90 backdrop-blur-sm",
                  selectedStatement === null && "hover:border-emerald/40 hover:bg-emerald/5",
                  isSelected && !showResult && "border-emerald bg-emerald/10",
                  showResult && isSelected && isLie && "border-red-400 bg-red-50",
                  showResult && isSelected && !isLie && "border-emerald bg-emerald/10",
                  showResult && !isSelected && isLie && "border-red-300 bg-red-50/50",
                  showResult && !isSelected && !isLie && "border-emerald/30 bg-emerald/5"
                )}
                onClick={() => selectedStatement === null && handleStatementSelect(index)}
              >
                <CardContent className="p-8">
                  <div className="flex items-start gap-4">
                    <div className={cn(
                      "flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold",
                      selectedStatement === null && "bg-grey/10 text-grey",
                      isSelected && !showResult && "bg-emerald text-white",
                      showResult && isLie && "bg-red-500 text-white",
                      showResult && !isLie && "bg-emerald text-white"
                    )}>
                      {index + 1}
                    </div>
                    
                    <div className="flex-1 space-y-3">
                      <p className="text-lg text-charcoal leading-relaxed">
                        {statement}
                      </p>
                      
                      {showResult && (
                        <div className="flex items-center gap-2">
                          {isLie ? (
                            <>
                              <XCircle className="h-5 w-5 text-red-500" />
                              <span className="text-red-600 font-medium">This is the LIE</span>
                            </>
                          ) : (
                            <>
                              <CheckCircle className="h-5 w-5 text-emerald" />
                              <span className="text-emerald font-medium">This is a FACT</span>
                            </>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {showResult && isSelected && (
                      <div className="flex-shrink-0">
                        {isLie ? (
                          <CheckCircle className="h-8 w-8 text-emerald" />
                        ) : (
                          <XCircle className="h-8 w-8 text-red-500" />
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Explanations */}
      <AnimatePresence>
        {showExplanations && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="bg-gradient-to-r from-bright-green/10 to-lime-green/10 rounded-2xl p-8 border border-bright-green/20">
              <div className="flex items-start gap-4 mb-6">
                <Lightbulb className="h-6 w-6 text-bright-green mt-1 flex-shrink-0" />
                <h4 className="text-xl font-semibold text-charcoal">Explanations:</h4>
              </div>
              
              <div className="space-y-4">
                {currentRound.explanations.map((explanation, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className={cn(
                      "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold",
                      index === currentRound.lieIndex ? "bg-red-500 text-white" : "bg-emerald text-white"
                    )}>
                      {index + 1}
                    </div>
                    <p className="text-grey leading-relaxed">{explanation}</p>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          onClick={previousRound}
          disabled={currentIndex === 0}
          variant="outline"
          className="border-emerald/30 text-emerald hover:bg-emerald/10 disabled:opacity-50"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <div className="text-center">
          <p className="text-sm text-grey">
            Press <kbd className="px-2 py-1 bg-grey/10 rounded text-xs">1</kbd>
            <kbd className="px-2 py-1 bg-grey/10 rounded text-xs ml-1">2</kbd>
            <kbd className="px-2 py-1 bg-grey/10 rounded text-xs ml-1">3</kbd> to select
          </p>
        </div>
        
        {showExplanations && (
          <Button
            onClick={nextRound}
            className="bg-emerald hover:bg-emerald-deep text-white"
          >
            {currentIndex === rounds.length - 1 ? "Complete Investigation" : "Next Case"}
            <ChevronRight className="h-4 w-4 ml-2" />
          </Button>
        )}
        
        {!showExplanations && (
          <div className="w-32" /> // Spacer to center the keyboard hint
        )}
      </div>
    </div>
  );
}
