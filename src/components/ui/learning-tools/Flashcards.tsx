"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/Card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  RotateCcw, 
  ChevronLeft, 
  ChevronRight, 
  Eye, 
  EyeOff, 
  Star, 
  Clock,
  Brain,
  Target,
  Shuffle,
  BookOpen,
  CheckCircle,
  XCircle,
  Lightbulb
} from "lucide-react";
import { toast } from "sonner";

interface FlashcardData {
  id: string;
  front: string;
  back: string;
  difficulty: "easy" | "medium" | "hard";
  category: string;
  hints?: string[];
}

interface FlashcardsProps {
  cards: FlashcardData[];
  planId: string;
  sessionId: string;
  onProgress?: (progress: any) => void;
  onComplete?: () => void;
}

export default function Flashcards({ 
  cards, 
  planId, 
  sessionId, 
  onProgress, 
  onComplete 
}: FlashcardsProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [showHints, setShowHints] = useState(false);
  const [studiedCards, setStudiedCards] = useState<Set<string>>(new Set());
  const [correctCards, setCorrectCards] = useState<Set<string>>(new Set());
  const [incorrectCards, setIncorrectCards] = useState<Set<string>>(new Set());
  const [sessionStartTime] = useState(Date.now());
  const [cardStartTime, setCardStartTime] = useState(Date.now());
  const [isShuffled, setIsShuffled] = useState(false);
  const [cardOrder, setCardOrder] = useState<number[]>([]);
  const [studyMode, setStudyMode] = useState<"study" | "review" | "test">("study");

  // Initialize card order
  useEffect(() => {
    setCardOrder(Array.from({ length: cards.length }, (_, i) => i));
  }, [cards.length]);

  const currentCard = cards[cardOrder[currentIndex]];
  const progress = (studiedCards.size / cards.length) * 100;
  const accuracy = studiedCards.size > 0 ? (correctCards.size / studiedCards.size) * 100 : 0;

  // Shuffle cards
  const shuffleCards = useCallback(() => {
    const newOrder = [...cardOrder];
    for (let i = newOrder.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newOrder[i], newOrder[j]] = [newOrder[j], newOrder[i]];
    }
    setCardOrder(newOrder);
    setCurrentIndex(0);
    setIsFlipped(false);
    setShowHints(false);
    setIsShuffled(true);
    toast.success("Cards shuffled!");
  }, [cardOrder]);

  // Navigation functions
  const goToNext = useCallback(() => {
    if (currentIndex < cards.length - 1) {
      setCurrentIndex(prev => prev + 1);
      setIsFlipped(false);
      setShowHints(false);
      setCardStartTime(Date.now());
    }
  }, [currentIndex, cards.length]);

  const goToPrevious = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
      setIsFlipped(false);
      setShowHints(false);
      setCardStartTime(Date.now());
    }
  }, [currentIndex]);

  // Flip card
  const flipCard = useCallback(() => {
    setIsFlipped(prev => !prev);
    if (!isFlipped) {
      setStudiedCards(prev => new Set([...prev, currentCard.id]));
    }
  }, [isFlipped, currentCard?.id]);

  // Mark card as correct/incorrect
  const markCard = useCallback((isCorrect: boolean) => {
    if (!currentCard) return;
    
    const timeSpent = Date.now() - cardStartTime;
    
    if (isCorrect) {
      setCorrectCards(prev => new Set([...prev, currentCard.id]));
      setIncorrectCards(prev => {
        const newSet = new Set(prev);
        newSet.delete(currentCard.id);
        return newSet;
      });
    } else {
      setIncorrectCards(prev => new Set([...prev, currentCard.id]));
      setCorrectCards(prev => {
        const newSet = new Set(prev);
        newSet.delete(currentCard.id);
        return newSet;
      });
    }

    // Auto-advance after marking
    setTimeout(() => {
      if (currentIndex < cards.length - 1) {
        goToNext();
      } else {
        // Session complete
        const totalTime = Math.round((Date.now() - sessionStartTime) / 1000 / 60);
        const finalAccuracy = Math.round(accuracy);
        
        onProgress?.({
          completed: true,
          score: finalAccuracy,
          timeSpent: totalTime,
          attempts: 1,
          details: {
            totalCards: cards.length,
            studiedCards: studiedCards.size,
            correctCards: correctCards.size,
            incorrectCards: incorrectCards.size,
            accuracy: finalAccuracy,
          }
        });
        
        onComplete?.();
        toast.success(`Session complete! Accuracy: ${finalAccuracy}%`);
      }
    }, 1000);
  }, [currentCard, cardStartTime, currentIndex, cards.length, goToNext, sessionStartTime, accuracy, studiedCards.size, correctCards.size, incorrectCards.size, onProgress, onComplete]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      switch (e.key) {
        case " ":
        case "Enter":
          e.preventDefault();
          flipCard();
          break;
        case "ArrowLeft":
          e.preventDefault();
          goToPrevious();
          break;
        case "ArrowRight":
          e.preventDefault();
          goToNext();
          break;
        case "h":
          e.preventDefault();
          setShowHints(prev => !prev);
          break;
        case "s":
          e.preventDefault();
          shuffleCards();
          break;
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [flipCard, goToPrevious, goToNext, shuffleCards]);

  if (!currentCard) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <BookOpen className="h-12 w-12 text-grey mx-auto mb-4" />
          <p className="text-grey">No flashcards available</p>
        </div>
      </div>
    );
  }

  const difficultyColors = {
    easy: "bg-lime-green/20 text-lime-green border-lime-green/30",
    medium: "bg-bright-green/20 text-bright-green border-bright-green/30",
    hard: "bg-emerald/20 text-emerald border-emerald/30"
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-8">
      {/* Header with Stats */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald/10 via-bright-green/10 to-lime-green/10 rounded-3xl blur-xl" />
        <div className="relative bg-white/80 backdrop-blur-lg rounded-3xl border border-white/20 p-8 shadow-2xl">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent">
                Interactive Flashcards
              </h2>
              <p className="text-grey text-lg">Master concepts through active recall</p>
            </div>
            
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center gap-2 px-4 py-2 bg-emerald/10 rounded-full">
                <Target className="h-4 w-4 text-emerald" />
                <span className="text-sm font-medium text-emerald">
                  {Math.round(accuracy)}% Accuracy
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-bright-green/10 rounded-full">
                <Brain className="h-4 w-4 text-bright-green" />
                <span className="text-sm font-medium text-bright-green">
                  {studiedCards.size}/{cards.length} Studied
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-lime-green/10 rounded-full">
                <Clock className="h-4 w-4 text-lime-green" />
                <span className="text-sm font-medium text-lime-green">
                  {Math.round((Date.now() - sessionStartTime) / 1000 / 60)}m
                </span>
              </div>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-6 space-y-2">
            <div className="flex justify-between text-sm text-grey">
              <span>Progress</span>
              <span>{currentIndex + 1} of {cards.length}</span>
            </div>
            <Progress 
              value={progress} 
              className="h-3 bg-grey/20"
            />
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-wrap items-center justify-center gap-3">
        <Button
          onClick={shuffleCards}
          variant="outline"
          size="sm"
          className="border-emerald/30 text-emerald hover:bg-emerald/10"
        >
          <Shuffle className="h-4 w-4 mr-2" />
          Shuffle
        </Button>
        
        <Button
          onClick={() => setShowHints(!showHints)}
          variant="outline"
          size="sm"
          className="border-bright-green/30 text-bright-green hover:bg-bright-green/10"
          disabled={!currentCard.hints?.length}
        >
          <Lightbulb className="h-4 w-4 mr-2" />
          {showHints ? "Hide" : "Show"} Hints
        </Button>
        
        <Badge className={cn("px-3 py-1", difficultyColors[currentCard.difficulty])}>
          {currentCard.difficulty.toUpperCase()}
        </Badge>
        
        <Badge variant="outline" className="px-3 py-1 border-grey/30 text-grey">
          {currentCard.category}
        </Badge>
      </div>

      {/* Main Flashcard */}
      <div className="relative perspective-1000">
        <motion.div
          className="relative w-full h-96 preserve-3d cursor-pointer"
          animate={{ rotateY: isFlipped ? 180 : 0 }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
          onClick={flipCard}
        >
          {/* Front of card */}
          <Card className="absolute inset-0 backface-hidden bg-gradient-to-br from-white via-white to-emerald/5 border-2 border-emerald/20 shadow-2xl hover:shadow-3xl transition-all duration-300">
            <CardContent className="h-full flex flex-col items-center justify-center p-8 text-center">
              <div className="space-y-6">
                <div className="p-4 bg-emerald/10 rounded-full w-fit mx-auto">
                  <BookOpen className="h-8 w-8 text-emerald" />
                </div>
                <h3 className="text-2xl font-bold text-charcoal leading-relaxed">
                  {currentCard.front}
                </h3>
                <p className="text-grey text-lg">Click to reveal answer</p>
              </div>
            </CardContent>
          </Card>

          {/* Back of card */}
          <Card className="absolute inset-0 backface-hidden rotate-y-180 bg-gradient-to-br from-white via-white to-bright-green/5 border-2 border-bright-green/20 shadow-2xl">
            <CardContent className="h-full flex flex-col justify-center p-8">
              <div className="space-y-6 text-center">
                <div className="p-4 bg-bright-green/10 rounded-full w-fit mx-auto">
                  <Eye className="h-8 w-8 text-bright-green" />
                </div>
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-charcoal">Answer:</h3>
                  <p className="text-lg text-charcoal leading-relaxed">
                    {currentCard.back}
                  </p>
                </div>
                
                {/* Action buttons when flipped */}
                <div className="flex items-center justify-center gap-4 pt-4">
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      markCard(false);
                    }}
                    variant="outline"
                    className="border-red-300 text-red-600 hover:bg-red-50"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Incorrect
                  </Button>
                  
                  <Button
                    onClick={(e) => {
                      e.stopPropagation();
                      markCard(true);
                    }}
                    className="bg-emerald hover:bg-emerald-deep text-white"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Correct
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Hints */}
      <AnimatePresence>
        {showHints && currentCard.hints?.length && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-gradient-to-r from-lime-green/10 to-bright-green/10 rounded-2xl p-6 border border-lime-green/20"
          >
            <div className="flex items-start gap-3">
              <Lightbulb className="h-5 w-5 text-lime-green mt-1 flex-shrink-0" />
              <div className="space-y-2">
                <h4 className="font-semibold text-charcoal">Hints:</h4>
                <ul className="space-y-1">
                  {currentCard.hints.map((hint, index) => (
                    <li key={index} className="text-grey">
                      • {hint}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Navigation */}
      <div className="flex items-center justify-between">
        <Button
          onClick={goToPrevious}
          disabled={currentIndex === 0}
          variant="outline"
          className="border-emerald/30 text-emerald hover:bg-emerald/10 disabled:opacity-50"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        
        <div className="text-center">
          <p className="text-sm text-grey">
            Press <kbd className="px-2 py-1 bg-grey/10 rounded text-xs">Space</kbd> to flip, 
            <kbd className="px-2 py-1 bg-grey/10 rounded text-xs ml-1">←→</kbd> to navigate
          </p>
        </div>
        
        <Button
          onClick={goToNext}
          disabled={currentIndex === cards.length - 1}
          variant="outline"
          className="border-emerald/30 text-emerald hover:bg-emerald/10 disabled:opacity-50"
        >
          Next
          <ChevronRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}
