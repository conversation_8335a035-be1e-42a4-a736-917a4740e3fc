import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";
import { createLearningPlan, updateLearningPlan, type LearningPlanInput } from "@/Services/planService";

// Simple schema without complex validation
const planRequestSchema = z.object({
  title: z.string().min(1),
  description: z.string().min(1),
  topic: z.string().min(1),
  certificateId: z.string().optional(),
  framework: z.string().optional(),
  intensity: z.enum(["detailed", "general", "simple"]),
  focusAreas: z.array(z.string()),
  locale: z.string(),
  userId: z.string().min(1),
});

const aiPlanSchema = z.object({
  objectives: z.array(z.string()).min(1).max(10),
  estimatedDuration: z.string(),
  difficulty: z.enum(["Beginner", "Intermediate", "Advanced"]),
  prerequisites: z.array(z.string()).max(8),
  enhancedDescription: z.string(),
  learningPath: z.array(z.object({
    step: z.number(),
    title: z.string(),
    description: z.string(),
    estimatedTime: z.string(),
    tools: z.array(z.string()),
  })).max(15),
  keyTopics: z.array(z.string()).max(12),
  practicalApplications: z.array(z.string()).max(8),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const parsed = planRequestSchema.parse(body);
    
    const {
      title,
      description,
      topic,
      certificateId,
      framework,
      intensity,
      focusAreas,
      locale,
      userId,
    } = parsed;

    // Create the basic plan first
    const planInput: LearningPlanInput = {
      title,
      description,
      topic,
      certificateId,
      framework,
      intensity,
      focusAreas,
      locale,
      userId,
    };

    const planId = await createLearningPlan(planInput);

    // Generate AI-enhanced content
    const systemPrompt = `You are an expert learning designer and educational AI. Create a comprehensive, personalized learning plan that maximizes engagement and knowledge retention.

Context:
- Topic: ${topic}
- Intensity Level: ${intensity}
- Focus Areas: ${focusAreas.join(", ")}
- Certificate Context: ${certificateId ? `Certificate: ${certificateId}` : "General learning"}
- Framework: ${framework || "General"}
- Locale: ${locale}

Your task is to design a learning plan that:
1. Breaks down complex topics into digestible steps
2. Provides clear, actionable objectives
3. Estimates realistic time commitments
4. Suggests appropriate learning tools for each step
5. Includes practical applications and real-world scenarios
6. Adapts to the specified intensity level

Available Learning Tools:
- Flashcards: For memorization and quick recall
- True/False: For concept validation and critical thinking
- Two Facts One Lie: For deeper understanding and analysis
- Knowledge Graphs: For visualizing relationships between concepts
- Practical Scenarios: For real-world application and decision making
- Match Concepts: For understanding connections and relationships
- Concepts Guided Learning: For step-by-step concept exploration

Intensity Guidelines:
- Simple: Basic concepts, shorter sessions, fewer prerequisites
- General: Balanced approach with moderate depth
- Detailed: Comprehensive coverage, longer sessions, extensive prerequisites

Create a learning plan that is engaging, practical, and tailored to the user's needs.`;

    const userPrompt = `Create a detailed learning plan for: "${title}"

Description: ${description}

Focus on these specific areas: ${focusAreas.join(", ")}

The plan should be ${intensity} level and suitable for ${locale === "ar" ? "Arabic" : "English"} speakers.

Provide:
1. Clear learning objectives (3-8 objectives)
2. Realistic time estimation for completion
3. Appropriate difficulty level assessment
4. Prerequisites if any
5. Enhanced description that motivates and explains the value
6. Step-by-step learning path with tool recommendations
7. Key topics to be covered
8. Practical applications and real-world scenarios

Make the content engaging, practical, and actionable.`;

    const result = await generateObject({
      model: google("gemini-2.5-pro"),
      maxTokens: 8000,
      schema: aiPlanSchema,
      prompt: `${systemPrompt}\n\n${userPrompt}`,
      temperature: 0.3,
    });

    // Update the plan with AI-generated content
    await updateLearningPlan(planId, {
      objectives: result.object.objectives,
      estimatedDuration: result.object.estimatedDuration,
      difficulty: result.object.difficulty,
      prerequisites: result.object.prerequisites,
      description: result.object.enhancedDescription,
      status: "active",
      progress: {
        currentStep: 0,
        totalSteps: result.object.learningPath.length,
        completedTools: [],
        timeSpent: 0,
        lastAccessedAt: new Date(),
      },
    });

    return Response.json({
      success: true,
      planId,
      plan: {
        ...result.object,
        id: planId,
        title,
        topic,
        intensity,
        focusAreas,
        locale,
      },
    });

  } catch (error: any) {
    console.error("Learning plan generation error:", error);

    if (error.name === "ZodError") {
      return Response.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return Response.json(
      { error: "Failed to generate learning plan", message: error.message },
      { status: 500 }
    );
  }
}

export async function GET(req: Request) {
  const url = new URL(req.url);
  const planId = url.searchParams.get("planId");
  
  if (!planId) {
    return Response.json({ error: "Plan ID is required" }, { status: 400 });
  }

  try {
    const { getLearningPlan } = await import("@/Services/planService");
    const plan = await getLearningPlan(planId);
    
    if (!plan) {
      return Response.json({ error: "Plan not found" }, { status: 404 });
    }

    return Response.json({ plan });
  } catch (error: any) {
    console.error("Error fetching learning plan:", error);
    return Response.json(
      { error: "Failed to fetch learning plan" },
      { status: 500 }
    );
  }
}
