import { google } from "@ai-sdk/google";
import { generateObject } from "ai";
import { z } from "zod";
import { getLearningPlan, createToolSession, updateToolSession } from "@/Services/planService";

// Tool-specific schemas
const flashcardsSchema = z.object({
  cards: z.array(z.object({
    id: z.string(),
    front: z.string(),
    back: z.string(),
    difficulty: z.enum(["easy", "medium", "hard"]),
    category: z.string(),
    hints: z.array(z.string()).optional(),
  })).min(5).max(20),
});

const trueOrFalseSchema = z.object({
  questions: z.array(z.object({
    id: z.string(),
    statement: z.string(),
    correct: z.boolean(),
    explanation: z.string(),
    difficulty: z.enum(["easy", "medium", "hard"]),
    category: z.string(),
  })).min(5).max(15),
});

const twoFactsOneLieSchema = z.object({
  rounds: z.array(z.object({
    id: z.string(),
    topic: z.string(),
    statements: z.array(z.string()).length(3),
    lieIndex: z.number().min(0).max(2),
    explanations: z.array(z.string()).length(3),
    difficulty: z.enum(["easy", "medium", "hard"]),
  })).min(3).max(10),
});

const knowledgeGraphSchema = z.object({
  nodes: z.array(z.object({
    id: z.string(),
    label: z.string(),
    type: z.enum(["concept", "skill", "application", "theory"]),
    description: z.string(),
    importance: z.enum(["low", "medium", "high"]),
  })).min(5).max(25),
  edges: z.array(z.object({
    from: z.string(),
    to: z.string(),
    relationship: z.string(),
    strength: z.number().min(1).max(10),
  })).min(4).max(40),
});

const practicalScenariosSchema = z.object({
  scenarios: z.array(z.object({
    id: z.string(),
    title: z.string(),
    context: z.string(),
    situation: z.string(),
    options: z.array(z.object({
      id: z.string(),
      action: z.string(),
      outcome: z.string(),
      isOptimal: z.boolean(),
      reasoning: z.string(),
    })).min(3).max(5),
    learningPoints: z.array(z.string()),
    difficulty: z.enum(["easy", "medium", "hard"]),
  })).min(3).max(8),
});

const matchConceptsSchema = z.object({
  exercises: z.array(z.object({
    id: z.string(),
    title: z.string(),
    leftItems: z.array(z.object({
      id: z.string(),
      content: z.string(),
      category: z.string(),
    })).min(4).max(8),
    rightItems: z.array(z.object({
      id: z.string(),
      content: z.string(),
      category: z.string(),
    })).min(4).max(8),
    correctMatches: z.array(z.object({
      leftId: z.string(),
      rightId: z.string(),
      explanation: z.string(),
    })),
    difficulty: z.enum(["easy", "medium", "hard"]),
  })).min(2).max(6),
});

const conceptsGuidedLearningSchema = z.object({
  modules: z.array(z.object({
    id: z.string(),
    title: z.string(),
    description: z.string(),
    steps: z.array(z.object({
      id: z.string(),
      title: z.string(),
      content: z.string(),
      type: z.enum(["explanation", "example", "practice", "reflection"]),
      interactiveElements: z.array(z.object({
        type: z.enum(["question", "exercise", "visualization"]),
        content: z.string(),
        answer: z.string().optional(),
      })).optional(),
    })).min(3).max(10),
    assessment: z.object({
      questions: z.array(z.object({
        question: z.string(),
        options: z.array(z.string()).min(2).max(4),
        correct: z.number(),
        explanation: z.string(),
      })).min(2).max(5),
    }),
    difficulty: z.enum(["easy", "medium", "hard"]),
  })).min(2).max(6),
});

const toolRequestSchema = z.object({
  planId: z.string().min(1),
  toolType: z.enum([
    "flashcards",
    "trueOrFalse", 
    "twoFactsOneLie",
    "knowledgeGraphs",
    "practicalScenarios",
    "matchConcepts",
    "conceptsGuidedLearning"
  ]),
  userId: z.string().min(1),
  focusArea: z.string().optional(),
  difficulty: z.enum(["easy", "medium", "hard"]).optional(),
  count: z.number().min(1).max(20).optional(),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const parsed = toolRequestSchema.parse(body);
    
    const { planId, toolType, userId, focusArea, difficulty, count } = parsed;

    // Get the learning plan
    const plan = await getLearningPlan(planId);
    if (!plan) {
      return Response.json({ error: "Learning plan not found" }, { status: 404 });
    }

    // Verify user access
    if (plan.userId !== userId) {
      return Response.json({ error: "Unauthorized access to plan" }, { status: 403 });
    }

    // Generate tool-specific content based on the plan
    const systemPrompt = `You are an expert educational content creator specializing in interactive learning tools. Create engaging, educational content that helps learners master the topic effectively.

Learning Plan Context:
- Topic: ${plan.topic}
- Objectives: ${plan.objectives.join(", ")}
- Difficulty Level: ${plan.difficulty}
- Focus Areas: ${plan.focusAreas.join(", ")}
- Intensity: ${plan.intensity}
- Locale: ${plan.locale}

Current Tool: ${toolType}
${focusArea ? `Specific Focus: ${focusArea}` : ""}
${difficulty ? `Target Difficulty: ${difficulty}` : ""}

Create content that:
1. Aligns with the learning objectives
2. Matches the specified difficulty level
3. Engages learners through interactivity
4. Provides clear explanations and feedback
5. Builds upon previous knowledge progressively
6. Includes practical applications where relevant

Make the content challenging but achievable, and ensure it contributes to the overall learning goals.`;

    let userPrompt = "";
    let schema: any;

    switch (toolType) {
      case "flashcards":
        schema = flashcardsSchema;
        userPrompt = `Create ${count || 10} flashcards for "${plan.topic}". Each card should test key concepts, definitions, or important facts. Include hints where helpful. Make the front concise and the back comprehensive but clear.`;
        break;

      case "trueOrFalse":
        schema = trueOrFalseSchema;
        userPrompt = `Create ${count || 8} true/false questions about "${plan.topic}". Include both obvious and subtle statements to test deep understanding. Provide detailed explanations for each answer.`;
        break;

      case "twoFactsOneLie":
        schema = twoFactsOneLieSchema;
        userPrompt = `Create ${count || 5} "Two Facts, One Lie" rounds about "${plan.topic}". Make the lie plausible but clearly incorrect to someone who understands the topic. Explain why each statement is true or false.`;
        break;

      case "knowledgeGraphs":
        schema = knowledgeGraphSchema;
        userPrompt = `Create a knowledge graph for "${plan.topic}". Include key concepts, their relationships, and how they connect. Show the hierarchical and associative relationships between different elements.`;
        break;

      case "practicalScenarios":
        schema = practicalScenariosSchema;
        userPrompt = `Create ${count || 4} practical scenarios related to "${plan.topic}". Each scenario should present a realistic situation where the learner must apply their knowledge to make decisions. Include multiple options with clear outcomes and reasoning.`;
        break;

      case "matchConcepts":
        schema = matchConceptsSchema;
        userPrompt = `Create ${count || 3} concept matching exercises for "${plan.topic}". Pair related concepts, definitions with terms, causes with effects, or problems with solutions. Provide clear explanations for each match.`;
        break;

      case "conceptsGuidedLearning":
        schema = conceptsGuidedLearningSchema;
        userPrompt = `Create ${count || 3} guided learning modules for "${plan.topic}". Each module should take learners through a complete concept with explanations, examples, practice, and assessment. Include interactive elements to maintain engagement.`;
        break;

      default:
        return Response.json({ error: "Invalid tool type" }, { status: 400 });
    }

    const result = await generateObject({
      model: google("gemini-2.5-pro"),
      maxTokens: 8000,
      schema,
      prompt: `${systemPrompt}\n\n${userPrompt}`,
      temperature: 0.4,
    });

    // Create a tool session
    const sessionId = await createToolSession(planId, toolType, userId, result.object);

    return Response.json({
      success: true,
      sessionId,
      toolType,
      content: result.object,
      planInfo: {
        id: plan.id,
        title: plan.title,
        topic: plan.topic,
      },
    });

  } catch (error: any) {
    console.error("Learning tool generation error:", error);
    
    if (error.name === "ZodError") {
      return Response.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return Response.json(
      { error: "Failed to generate learning tool content", message: error.message },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const body = await req.json();
    const { planId, sessionId, progress, userId } = body;

    if (!planId || !sessionId || !userId) {
      return Response.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Verify plan access
    const plan = await getLearningPlan(planId);
    if (!plan || plan.userId !== userId) {
      return Response.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Update session progress
    await updateToolSession(planId, sessionId, { progress });

    return Response.json({ success: true });

  } catch (error: any) {
    console.error("Error updating tool session:", error);
    return Response.json(
      { error: "Failed to update session" },
      { status: 500 }
    );
  }
}
