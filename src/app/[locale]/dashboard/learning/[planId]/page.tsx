"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useLocale, useTranslations } from "next-intl";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useUser } from "@/hooks/useUser";
import { DtcHero } from "@/components/ui/Hero";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/Card";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  BookOpen, 
  Clock, 
  Target, 
  Brain,
  Loader2,
  AlertCircle,
  CheckCircle,
  Star,
  TrendingUp,
  Award,
  RotateCcw
} from "lucide-react";
import { toast } from "sonner";
import { type LearningPlan, getLearningPlan, updateLearningPlan } from "@/Services/planService";
import LearningLesson from "@/components/ui/learning-hub/LearningLesson";

export default function LearningPlanPage() {
  const params = useParams();
  const router = useRouter();
  const locale = useLocale();
  const { user } = useUser();
  
  const planId = Array.isArray(params?.planId) 
    ? params?.planId[0] 
    : (params?.planId as string | undefined);

  const [plan, setPlan] = useState<LearningPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);

  // Load the learning plan
  useEffect(() => {
    async function loadPlan() {
      if (!planId || !user) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const planData = await getLearningPlan(planId);
        
        if (!planData) {
          setError("Learning plan not found");
          return;
        }
        
        // Verify user access
        if (planData.userId !== user.uid) {
          setError("You don't have access to this learning plan");
          return;
        }
        
        setPlan(planData);
      } catch (err) {
        console.error("Error loading plan:", err);
        setError("Failed to load learning plan");
      } finally {
        setLoading(false);
      }
    }
    
    loadPlan();
  }, [planId, user]);

  // Handle plan updates
  const handlePlanUpdate = async (updates: Partial<LearningPlan>) => {
    if (!planId || !plan) return;
    
    setIsUpdating(true);
    try {
      await updateLearningPlan(planId, updates);
      setPlan(prev => prev ? { ...prev, ...updates } : null);
    } catch (err) {
      console.error("Error updating plan:", err);
      toast.error("Failed to update progress");
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle plan completion
  const handlePlanComplete = () => {
    toast.success("🎉 Congratulations! You've completed your learning plan!");
    // Could redirect to a completion page or show completion modal
  };

  // Navigate back to learning hub
  const goBack = () => {
    router.push(`/${locale}/dashboard/knowledge-hub`);
  };

  if (loading) {
    return (
      <div className="w-full">
        <DtcHero
          title="Loading Learning Plan"
          subtitle="Please wait while we prepare your personalized learning experience"
          className="mb-8"
        />
        
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-4">
            <Loader2 className="h-12 w-12 text-emerald animate-spin mx-auto" />
            <p className="text-grey text-lg">Loading your learning plan...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <DtcHero
          title="Learning Plan Error"
          subtitle="We encountered an issue loading your learning plan"
          className="mb-8"
        />
        
        <div className="max-w-2xl mx-auto">
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-8 text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-red-700 mb-2">
                {error}
              </h3>
              <p className="text-red-600 mb-6">
                Please check the URL or try again later.
              </p>
              <div className="flex items-center justify-center gap-4">
                <Button
                  onClick={goBack}
                  variant="outline"
                  className="border-red-300 text-red-600 hover:bg-red-50"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Learning Hub
                </Button>
                <Button
                  onClick={() => window.location.reload()}
                  className="bg-red-600 hover:bg-red-700 text-white"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!plan) {
    return null;
  }

  const completedTools = plan.progress.completedTools || [];
  const enabledTools = Object.entries(plan.tools).filter(([_, enabled]) => enabled).map(([tool]) => tool);
  const overallProgress = enabledTools.length > 0 ? (completedTools.length / enabledTools.length) * 100 : 0;
  const isCompleted = completedTools.length === enabledTools.length && enabledTools.length > 0;

  return (
    <div className="w-full space-y-8">
      {/* Hero Header */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-emerald/5 via-bright-green/5 to-lime-green/5 rounded-3xl blur-xl" />
        <div className="relative bg-white/80 backdrop-blur-lg rounded-3xl border border-white/20 p-8 shadow-2xl">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Button
                  onClick={goBack}
                  variant="outline"
                  size="sm"
                  className="border-emerald/30 text-emerald hover:bg-emerald/10"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
                
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="border-grey/30 text-grey">
                    {plan.framework || "General"}
                  </Badge>
                  <Badge className="bg-emerald text-white">
                    {plan.intensity.charAt(0).toUpperCase() + plan.intensity.slice(1)}
                  </Badge>
                </div>
              </div>
              
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-emerald to-bright-green bg-clip-text text-transparent">
                  {plan.title}
                </h1>
                <p className="text-grey text-lg mt-2">
                  Topic: {plan.topic}
                </p>
              </div>
            </div>
            
            {/* Progress Stats */}
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center gap-2 px-4 py-2 bg-emerald/10 rounded-full">
                <Target className="h-4 w-4 text-emerald" />
                <span className="text-sm font-medium text-emerald">
                  {Math.round(overallProgress)}% Complete
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-bright-green/10 rounded-full">
                <Brain className="h-4 w-4 text-bright-green" />
                <span className="text-sm font-medium text-bright-green">
                  {completedTools.length}/{enabledTools.length} Tools
                </span>
              </div>
              
              <div className="flex items-center gap-2 px-4 py-2 bg-lime-green/10 rounded-full">
                <Clock className="h-4 w-4 text-lime-green" />
                <span className="text-sm font-medium text-lime-green">
                  {Math.round((plan.progress.timeSpent || 0))}m spent
                </span>
              </div>
              
              {isCompleted && (
                <div className="flex items-center gap-2 px-4 py-2 bg-yellow-100 rounded-full">
                  <Award className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-600">
                    Completed!
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Plan Status */}
      {isCompleted && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-emerald/10 to-bright-green/10 rounded-2xl p-8 border border-emerald/20"
        >
          <div className="flex items-center gap-4">
            <div className="p-3 bg-emerald/20 rounded-full">
              <CheckCircle className="h-8 w-8 text-emerald" />
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-charcoal mb-2">
                🎉 Learning Plan Completed!
              </h3>
              <p className="text-grey">
                Congratulations! You've successfully completed all learning tools in this plan. 
                You can continue practicing or explore other learning plans.
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Focus Areas */}
      {plan.focusAreas && plan.focusAreas.length > 0 && (
        <div className="bg-gradient-to-r from-bright-green/5 to-lime-green/5 rounded-2xl p-6 border border-bright-green/20">
          <div className="flex items-start gap-3">
            <Star className="h-5 w-5 text-bright-green mt-1 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-charcoal mb-2">Focus Areas</h3>
              <div className="flex flex-wrap gap-2">
                {plan.focusAreas.map((area, index) => (
                  <Badge 
                    key={index} 
                    variant="outline" 
                    className="border-bright-green/30 text-bright-green"
                  >
                    {area}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Learning Lesson Component */}
      <LearningLesson
        plan={plan}
        onPlanUpdate={handlePlanUpdate}
        onComplete={handlePlanComplete}
      />

      {/* Update Indicator */}
      {isUpdating && (
        <div className="fixed bottom-4 right-4 bg-emerald text-white px-4 py-2 rounded-full shadow-lg flex items-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span className="text-sm">Saving progress...</span>
        </div>
      )}
    </div>
  );
}
